#!/usr/bin/env python3
import requests
import json

# 测试菜单API
def test_menu_api():
    base_url = "http://127.0.0.1:8000"
    
    # 1. 先登录获取token
    login_data = {
        "userName": "super",
        "password": "admin123"
    }
    
    try:
        print("🔐 正在登录...")
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('data', {}).get('token')
            print(f"✅ 登录成功，获取到token: {token[:20]}...")
            
            # 2. 使用token调用菜单API
            print("\n📋 正在获取菜单数据...")
            menu_response = requests.get(
                f"{base_url}/api/v1/menu/list/",
                headers={
                    "Authorization": f"Bearer {token}",  # 添加Bearer前缀
                    "Content-Type": "application/json"
                }
            )
            
            if menu_response.status_code == 200:
                menu_data = menu_response.json()
                print("✅ 菜单API调用成功！")
                print("\n📋 菜单数据结构：")
                print(json.dumps(menu_data, indent=2, ensure_ascii=False))
                
                # 分析菜单内容
                menu_list = menu_data.get('data', {}).get('menuList', [])
                print(f"\n📊 菜单统计：")
                print(f"- 总菜单数量: {len(menu_list)}")
                for i, menu in enumerate(menu_list, 1):
                    name = menu.get('name', 'Unknown')
                    title = menu.get('meta', {}).get('title', 'No title')
                    children_count = len(menu.get('children', []))
                    print(f"- {i}. {name} ({title}) - {children_count} 个子菜单")
                    
            else:
                print(f"❌ 菜单API调用失败: {menu_response.status_code}")
                print(f"错误信息: {menu_response.text}")
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"错误信息: {login_response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    test_menu_api()
