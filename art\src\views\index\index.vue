<template>
  <!-- 布局容器 -->
  <ArtLayouts>
    <!-- 顶栏、水平/混合菜单 -->
    <ArtHeaderBar />
    <!-- 左侧/双列菜单 -->
    <ArtSidebarMenu />
    <!-- 页面内容 -->
    <ArtPageContent />
    <!-- 设置面板 -->
    <ArtSettingsPanel />
    <!-- 全局搜索 -->
    <ArtGlobalSearch />
    <!-- 屏幕锁定 -->
    <ArtScreenLock />
    <!-- 聊天窗口 -->
    <ArtChatWindow />
    <!-- 礼花效果 -->
    <ArtFireworksEffect />
    <!-- 水印效果 -->
    <ArtWatermark />
  </ArtLayouts>
</template>

<script setup lang="ts">
  import ArtChatWindow from '@/components/core/layouts/art-chat-window/index.vue'
  import ArtFireworksEffect from '@/components/core/layouts/art-fireworks-effect/index.vue'
  import ArtGlobalSearch from '@/components/core/layouts/art-global-search/index.vue'
  import ArtHeaderBar from '@/components/core/layouts/art-header-bar/index.vue'
  import ArtLayouts from '@/components/core/layouts/art-layouts/index.vue'
  import ArtSidebarMenu from '@/components/core/layouts/art-menus/art-sidebar-menu/index.vue'
  import ArtPageContent from '@/components/core/layouts/art-page-content/index.vue'
  import ArtScreenLock from '@/components/core/layouts/art-screen-lock/index.vue'
  import ArtSettingsPanel from '@/components/core/layouts/art-settings-panel/index.vue'
  import ArtWatermark from '@/components/core/others/art-watermark/index.vue'

  defineOptions({ name: 'IndexLayout' })
</script>

<style lang="scss" scoped>
  @use './style';
</style>
