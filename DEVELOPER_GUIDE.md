# 开发者控制台使用指南

## 🎯 概述

开发者控制台是专为系统开发者设计的综合管理工具，提供系统监控、调试、测试和亚马逊API管理等功能。

## 🔐 开发者账户

**登录信息：**
- 用户名: `developer`
- 密码: `dev123456`
- 角色: 开发者（拥有完整的开发和系统管理权限）

## 📋 功能模块

### 🔧 系统工具

#### 1. 系统监控 (`/developer/system-monitor`)
- **功能**: 实时监控系统状态、性能指标和服务健康度
- **特性**:
  - CPU和内存使用率监控
  - 服务状态检查（Django、Vue、Redis、Celery）
  - 数据库连接状态
  - 实时日志查看
  - 性能图表展示

#### 2. 数据库工具 (`/developer/database-tools`)
- **功能**: SQL查询、数据导入导出、数据库结构查看
- **特性**:
  - SQL查询编辑器
  - 查询结果导出
  - 数据库表结构查看
  - 常用查询模板
  - 支持多数据库切换

#### 3. API调试 (`/developer/api-debug`)
- **功能**: 接口测试、请求日志、响应分析
- **特性**:
  - RESTful API测试
  - 请求/响应日志
  - 接口性能分析
  - 批量接口测试

#### 4. 日志查看 (`/developer/logs`)
- **功能**: 应用日志、错误日志、用户操作日志查看
- **特性**:
  - 多级别日志过滤
  - 实时日志流
  - 日志搜索和导出
  - 错误统计分析

#### 5. 性能分析 (`/developer/performance`)
- **功能**: 慢查询分析、接口响应时间、资源使用分析
- **特性**:
  - SQL慢查询监控
  - API响应时间统计
  - 内存使用分析
  - 性能瓶颈识别

### 🧪 测试工具

#### 6. 测试数据生成 (`/developer/test-data`)
- **功能**: 批量生成测试用户、产品、订单数据
- **特性**:
  - 自定义数据模板
  - 批量数据生成
  - 数据关联性保证
  - 测试环境隔离

#### 7. 用户模拟 (`/developer/user-simulation`)
- **功能**: 模拟不同角色用户进行功能测试
- **特性**:
  - 多角色切换
  - 权限测试
  - 用户行为模拟
  - 场景测试

#### 8. 功能测试 (`/developer/function-test`)
- **功能**: 自动化测试、接口测试、压力测试
- **特性**:
  - 单元测试执行
  - 集成测试管理
  - 压力测试工具
  - 测试报告生成

### 🛒 亚马逊API管理

#### 9. Amazon API配置 (`/developer/amazon-config`)
- **功能**: SP-API密钥配置、区域设置
- **特性**:
  - SP-API密钥管理
  - 多区域配置（北美、欧洲、远东）
  - 环境切换（沙盒/生产）
  - 连接测试

#### 10. Amazon数据同步 (`/developer/amazon-sync`)
- **功能**: 商品信息、库存、订单数据同步
- **特性**:
  - 商品信息同步
  - 库存数据更新
  - 订单状态同步
  - 同步日志记录

#### 11. Amazon报告管理 (`/developer/amazon-reports`)
- **功能**: 销售报告、库存报告、广告报告管理
- **特性**:
  - 报告请求管理
  - 报告下载和解析
  - 数据可视化
  - 定时报告任务

#### 12. Amazon广告API (`/developer/amazon-ads`)
- **功能**: 广告活动管理、关键词优化
- **特性**:
  - 广告活动监控
  - 关键词管理
  - 出价优化
  - 广告效果分析

#### 13. Amazon SP-API测试 (`/developer/amazon-sp-api`)
- **功能**: API接口测试和调试
- **特性**:
  - 接口调用测试
  - 参数验证
  - 响应分析
  - 错误诊断

### ⚙️ 系统配置

#### 14. 环境变量 (`/developer/env-config`)
- **功能**: 系统配置参数管理
- **特性**:
  - 环境变量编辑
  - 配置热更新
  - 配置备份恢复
  - 敏感信息加密

#### 15. 功能开关 (`/developer/feature-flags`)
- **功能**: 新功能灰度发布控制
- **特性**:
  - 功能开关管理
  - 用户群体控制
  - A/B测试支持
  - 发布策略配置

#### 16. 缓存管理 (`/developer/cache-management`)
- **功能**: Redis缓存查看和清理
- **特性**:
  - 缓存键查看
  - 缓存数据清理
  - 缓存性能监控
  - 缓存策略优化

#### 17. 任务调度 (`/developer/task-scheduler`)
- **功能**: 定时任务管理和监控
- **特性**:
  - Celery任务监控
  - 定时任务配置
  - 任务执行日志
  - 任务性能分析

## 🚀 快速开始

### 1. 登录系统
```
用户名: developer
密码: dev123456
```

### 2. 访问开发者控制台
登录后，在左侧菜单中找到"开发者控制台"，点击展开查看所有子功能。

### 3. 配置亚马逊API
1. 进入"Amazon API配置"
2. 添加SP-API密钥
3. 配置区域和环境
4. 测试连接

### 4. 监控系统状态
1. 进入"系统监控"
2. 查看实时性能指标
3. 监控服务状态
4. 查看系统日志

## 💡 最佳实践

### 开发环境使用
- 使用沙盒环境进行API测试
- 定期清理测试数据
- 监控系统性能指标

### 生产环境使用
- 谨慎使用数据库工具
- 定期备份重要配置
- 监控API调用频率

### 安全注意事项
- 不要在生产环境暴露敏感信息
- 定期更新API密钥
- 监控异常访问日志

## 🔧 故障排除

### 常见问题
1. **API连接失败**: 检查密钥配置和网络连接
2. **数据同步异常**: 查看同步日志和错误信息
3. **性能问题**: 使用性能分析工具定位瓶颈

### 联系支持
如果遇到问题，可以：
1. 查看系统日志获取详细错误信息
2. 使用API调试工具测试接口
3. 检查系统监控指标

## 📝 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 包含17个开发者工具
- 支持亚马逊API管理
- 完整的系统监控功能

---

**开发者控制台** - 让开发更高效，让系统更稳定！ 🚀
