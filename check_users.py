#!/usr/bin/env python3
"""
检查数据库中的用户
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('ep_system')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
os.chdir('ep_system')
django.setup()

from core.models import CustomUser

def check_users():
    """检查数据库中的用户"""
    print("🔍 检查数据库中的用户...")
    
    users = CustomUser.objects.all()
    print(f"📊 总用户数: {users.count()}")
    
    for user in users:
        print(f"  - ID: {user.id}")
        print(f"    用户名: {user.username}")
        print(f"    角色: {user.role} ({user.get_role_display()})")
        print(f"    区域: {user.region.name if user.region else 'None'}")
        print(f"    激活状态: {user.is_active}")
        print(f"    创建时间: {user.date_joined}")
        print(f"    最后登录: {user.last_login}")
        print("    ---")

def create_super_user():
    """创建超级管理员用户"""
    print("🔧 创建超级管理员用户...")
    
    # 检查是否已存在super用户
    if CustomUser.objects.filter(username='super').exists():
        print("⚠️ super用户已存在，删除旧用户...")
        CustomUser.objects.filter(username='super').delete()
    
    # 创建新的super用户
    user = CustomUser.objects.create_user(
        username='super',
        password='admin123',
        role='SUPER_ADMIN',
        phone='13800000000'
    )
    
    print(f"✅ 创建成功: {user.username} (ID: {user.id})")
    return user

if __name__ == "__main__":
    print("🚀 开始检查用户数据...")
    
    # 检查现有用户
    check_users()
    
    # 创建超级管理员
    super_user = create_super_user()
    
    print("\n🔍 重新检查用户数据...")
    check_users()
