"""
Core应用的Celery任务
"""
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
import time
import logging

logger = logging.getLogger(__name__)

@shared_task
def send_notification_email(subject, message, recipient_list):
    """
    发送通知邮件的异步任务
    """
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipient_list,
            fail_silently=False,
        )
        logger.info(f"邮件发送成功: {subject} -> {recipient_list}")
        return f"邮件发送成功: {len(recipient_list)} 个收件人"
    except Exception as e:
        logger.error(f"邮件发送失败: {str(e)}")
        raise

@shared_task
def cleanup_old_logs():
    """
    清理旧日志的定时任务
    """
    try:
        # 这里可以添加清理逻辑
        # 例如：删除30天前的日志记录
        logger.info("开始清理旧日志...")
        time.sleep(2)  # 模拟处理时间
        logger.info("旧日志清理完成")
        return "旧日志清理完成"
    except Exception as e:
        logger.error(f"清理旧日志失败: {str(e)}")
        raise

@shared_task
def sync_user_permissions():
    """
    同步用户权限的任务
    """
    try:
        from core.models import CustomUser
        
        logger.info("开始同步用户权限...")
        users = CustomUser.objects.filter(is_active=True)
        
        for user in users:
            # 这里可以添加权限同步逻辑
            pass
            
        logger.info(f"用户权限同步完成，处理了 {users.count()} 个用户")
        return f"用户权限同步完成，处理了 {users.count()} 个用户"
    except Exception as e:
        logger.error(f"用户权限同步失败: {str(e)}")
        raise

@shared_task
def generate_system_report():
    """
    生成系统报表的任务
    """
    try:
        logger.info("开始生成系统报表...")
        
        # 模拟报表生成过程
        time.sleep(5)
        
        report_data = {
            'total_users': 150,
            'active_users': 120,
            'total_regions': 6,
            'generated_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        logger.info("系统报表生成完成")
        return report_data
    except Exception as e:
        logger.error(f"系统报表生成失败: {str(e)}")
        raise