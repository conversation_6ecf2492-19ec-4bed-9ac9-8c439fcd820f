/**
 * namespace: Api
 *
 * 所有接口相关类型定义
 * 在.vue文件使用会报错，需要在 eslint.config.mjs 中配置 globals: { Api: 'readonly' }
 */
declare namespace Api {
  /** 基础类型 */
  namespace Http {
    /** 基础响应 */
    interface BaseResponse<T = any> {
      // 状态码
      code: number
      // 消息
      msg: string
      // 数据
      data: T
    }
  }

  /** 通用类型 */
  namespace Common {
    /** 分页参数 */
    interface PaginatingParams {
      /** 当前页码 */
      current: number
      /** 每页条数 */
      size: number
      /** 总条数 */
      total: number
    }

    /** 通用搜索参数 */
    type PaginatingSearchParams = Pick<PaginatingParams, 'current' | 'size'>

    /** 启用状态 */
    type EnableStatus = '1' | '2'

    /** 通用响应 */
    interface CommonResponse {
      code: number
      msg: string
      data: any
    }

    /** 助理信息 */
    interface AssistantInfo {
      id: number
      username: string
      phone: string
      regionName: string
      managedMemberCount: number
    }

    /** 权限信息 */
    interface PermissionInfo {
      id: number
      name: string
      code: string
      description: string
      category: string
    }

    /** 权限列表响应 */
    interface PermissionListResponse {
      permissions: PermissionInfo[]
      categories: Record<string, PermissionInfo[]>
    }

    /** 会员信息 */
    interface MemberInfo {
      id: number
      username: string
      phone: string
      regionName: string
      managed_by: {
        id: number
        username: string
      } | null
    }
  }

  /** 认证类型 */
  namespace Auth {
    /** 登录参数 */
    interface LoginParams {
      userName: string
      password: string
    }

    /** 登录响应 */
    interface LoginResponse {
      token: string
      refreshToken: string
      userInfo: {
        id: number
        username: string
        role: string
        roleDisplay: string
        regionId: number | null
        regionName: string
        regionCode: string
        phone?: string
      }
    }
  }

  /** 用户类型 */
  namespace User {
    /** 用户信息 */
    interface UserInfo {
      userId: number
      userName: string
      roles: string[]
      buttons: string[]
      avatar?: string
      phone?: string
      role?: string
      roleDisplay?: string
      regionId?: number | null
      regionName?: string
      regionCode?: string
    }

    /** 用户列表数据 */
    interface UserListData {
      records: UserListItem[]
      current: number
      size: number
      total: number
    }

    /** 用户列表项 */
    interface UserListItem {
      id: number
      avatar: string
      createBy: string
      createTime: string
      updateBy: string
      updateTime: string
      status: '1' | '2' | '3' | '4' // 1: 在线 2: 离线 3: 异常 4: 注销
      userName: string
      userGender: string
      nickName: string
      userPhone: string
      userRoles: string[]
      role?: string
      roleDisplay?: string
      regionId?: number | null
      regionName?: string
      regionCode?: string
      managedById?: number | null
      managedByName?: string | null
      isActive?: boolean
      lastLoginTime?: string
      lastLoginIP?: string
      mwsSwitch?: boolean
      hasRefreshToken?: boolean
      memberProfile?: MemberProfile
      assistantProfile?: AssistantProfile
    }

    /** 创建用户参数 */
    interface CreateUserParams {
      username: string
      phone?: string
      role?: string
      password?: string
      managed_by_id?: number // 负责助理ID（仅对MEMBER有效）
      permission_ids?: number[] // 权限ID列表
      responsible_member_ids?: number[] // 助理负责的会员ID列表（仅对MEMBER_ASSISTANT有效）
    }

    /** 创建用户响应 */
    interface CreateUserResponse {
      id: number
      username: string
      phone: string
      role: string
      roleDisplay: string
      regionName: string
      defaultPassword: string
    }

    /** 更新用户参数 */
    interface UpdateUserParams {
      username?: string
      phone?: string
      role?: string
      permission_ids?: number[]
      responsible_member_ids?: number[]
      mws_switch?: boolean
    }

    /** 更新用户响应 */
    interface UpdateUserResponse {
      id: number
      username: string
      phone: string
      role: string
      roleDisplay: string
    }

    /** 会员Profile信息 */
    interface MemberProfile {
      real_name?: string
      english_name?: string
      description?: string
      image_server_url?: string
      document_server_url?: string
      finance_auth_token?: string
      sku_prefix?: string
      payment_channel?: string
      parent_account?: string
      ems_vip_number?: string
      ems_customer_code?: string
      ems_version_info?: string
      international_eub_us?: string
      ems_auth_token?: string
      amazon_username?: string
      amazon_seller_id?: string
      amazon_marketplace_id?: string
      aws_access_key_id?: string
      aws_secret_key?: string
      merchant_token?: string
      developer_aws_access?: string
      developer_secret_key?: string
      mws_auth_token?: string
      warehouse_location?: string
      status?: string
      exempt_brands?: string
    }

    /** 助理Profile信息 */
    interface AssistantProfile {
      description?: string
      related_finance_auth_token?: string
      internal_member_finance_auth_token?: string
    }

    /** 用户详情信息 */
    interface UserDetailInfo {
      id: number
      username: string
      phone: string
      role: string
      roleDisplay: string
      regionName: string | null
      managedBy: {
        id: number
        username: string
      } | null
      permissions: Common.PermissionInfo[]
      responsibleMembers: Common.MemberInfo[]
      member_profile?: MemberProfile
      assistant_profile?: AssistantProfile
    }

    /** 用户详情响应 */
    interface UserDetailResponse {
      code: number
      msg: string
      data: UserDetailInfo
    }

    /** 刷新令牌响应 */
    interface RefreshTokenResponse {
      code: number
      msg: string
      data: {
        username: string
        refresh_token: string
      }
    }
  }
}
