#!/usr/bin/env python3
"""
测试修复后的API功能
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """测试登录功能"""
    print("🔐 测试登录...")

    login_data = {"userName": "super", "password": "admin123"}

    response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
    print(f"登录响应状态: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"✅ 登录成功: {data['msg']}")
        return data['data']['token']
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_menu_api(token):
    """测试菜单API"""
    print("\n📋 测试菜单API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/menu/list/", headers=headers)
    print(f"菜单API响应状态: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 菜单获取成功: {data['msg']}")
        print(f"📋 菜单数量: {len(data['data']['menuList'])}")
        
        # 打印菜单结构
        for menu in data['data']['menuList']:
            print(f"  - {menu['name']} ({menu['path']})")
            if menu.get('children'):
                for child in menu['children']:
                    print(f"    - {child['name']} ({child['path']})")
        return True
    else:
        print(f"❌ 菜单获取失败: {response.text}")
        return False

def test_user_list_api(token):
    """测试用户列表API"""
    print("\n👥 测试用户列表API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试基本用户列表
    response = requests.get(f"{BASE_URL}/user/list/", headers=headers)
    print(f"用户列表API响应状态: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 用户列表获取成功: {data['msg']}")
        print(f"👥 用户总数: {data['data']['total']}")
        print(f"📄 当前页用户数: {len(data['data']['records'])}")
        
        # 打印用户信息
        for user in data['data']['records']:
            print(f"  - {user['username']} ({user['roleDisplay']}) - {user['regionName'] or 'No Region'}")
        
        # 测试分页
        print("\n📄 测试分页...")
        response = requests.get(f"{BASE_URL}/user/list/?current=1&size=5", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 分页测试成功，返回 {len(data['data']['records'])} 条记录")
        
        # 测试搜索
        print("\n🔍 测试搜索...")
        response = requests.get(f"{BASE_URL}/user/list/?name=super", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 搜索测试成功，找到 {len(data['data']['records'])} 条记录")
        
        return True
    else:
        print(f"❌ 用户列表获取失败: {response.text}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的API功能...")
    
    # 1. 测试登录
    token = test_login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    # 2. 测试菜单API
    menu_success = test_menu_api(token)
    
    # 3. 测试用户列表API
    user_success = test_user_list_api(token)
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 登录功能: ✅")
    print(f"  - 菜单API: {'✅' if menu_success else '❌'}")
    print(f"  - 用户列表API: {'✅' if user_success else '❌'}")
    
    if menu_success and user_success:
        print("\n🎉 所有API测试通过！")
    else:
        print("\n⚠️ 部分API测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
