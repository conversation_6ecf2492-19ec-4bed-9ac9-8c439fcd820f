<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="3ad28fd3-fef6-4840-b7f3-492e5a358fbf" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/art/.env" beforeDir="false" afterPath="$PROJECT_DIR$/art/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/art/README.zh-CN.md" beforeDir="false" afterPath="$PROJECT_DIR$/art/README.zh-CN.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/scripts/clean-dev.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/scripts/clean-dev.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/api/menuApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/api/menuApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/api/usersApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/api/usersApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/components/core/layouts/art-header-bar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/components/core/layouts/art-header-bar/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/config/dataSource.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/config/dataSource.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/config/headerBar.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/config/headerBar.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/config/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/config/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/locales/langs/en.json" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/locales/langs/en.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/locales/langs/zh.json" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/locales/langs/zh.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/router/guards/beforeEach.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/router/guards/beforeEach.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/router/routesAlias.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/router/routesAlias.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/router/utils/registerRoutes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/router/utils/registerRoutes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/store/modules/worktab.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/store/modules/worktab.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/typings/api.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/typings/api.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/utils/http/error.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/utils/http/error.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/utils/http/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/utils/http/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/utils/sys/console.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/auth/login/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/auth/login/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/examples/tables/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/examples/tables/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/index/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/index/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/system/menu/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/system/menu/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/system/user-center/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/system/user-center/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/system/user/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/system/user/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/system/user/modules/user-dialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/system/user/modules/user-dialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/system/user/modules/user-search.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/system/user/modules/user-search.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/art/src/views/widgets/text-scroll/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/art/src/views/widgets/text-scroll/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/art" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="2zzaFecCGZZG4uAxhW6XbCkUwKz" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "my",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/web/Dawn_ERP_V1.0",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-WS-251.25410.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3ad28fd3-fef6-4840-b7f3-492e5a358fbf" name="更改" comment="" />
      <created>1752736252653</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752736252653</updated>
      <workItem from="1752736253918" duration="393000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>