<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试其他用户登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .user-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .user-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .user-card:hover {
            background: #e9ecef;
        }
        .user-card.selected {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 测试其他用户登录</h1>
        
        <div class="form-group">
            <label>选择测试用户：</label>
            <div class="user-list" id="userList">
                <!-- 用户卡片将通过JavaScript生成 -->
            </div>
        </div>
        
        <div class="form-group">
            <label for="username">用户名：</label>
            <input type="text" id="username" placeholder="选择用户或手动输入">
        </div>
        
        <div class="form-group">
            <label for="password">密码：</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>
        
        <button onclick="testLogin()" id="loginBtn">🚀 测试登录</button>
        <button onclick="clearResult()">🧹 清空结果</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 测试用户列表
        const testUsers = [
            { username: 'super', role: '超级管理员', region: '全局' },
            { username: 'mpr_admin', role: '区域管理员', region: 'MPR区域' },
            { username: 'rl_admin', role: '区域管理员', region: 'RL区域' },
            { username: 'eo_admin', role: '区域管理员', region: 'EO区域' },
            { username: 'zz_admin', role: '区域管理员', region: 'ZZ区域' },
            { username: 'wh_admin', role: '区域管理员', region: 'WH区域' },
            { username: 'MM', role: '普通会员', region: 'MPR区域' },
            { username: 'zhs', role: '会员助理', region: 'MPR区域' },
        ];
        
        // 生成用户卡片
        function generateUserCards() {
            const userList = document.getElementById('userList');
            userList.innerHTML = '';
            
            testUsers.forEach(user => {
                const card = document.createElement('div');
                card.className = 'user-card';
                card.innerHTML = `
                    <strong>${user.username}</strong><br>
                    <small>${user.role}</small><br>
                    <small>${user.region}</small>
                `;
                card.onclick = () => selectUser(user.username, card);
                userList.appendChild(card);
            });
        }
        
        // 选择用户
        function selectUser(username, cardElement) {
            // 清除其他选中状态
            document.querySelectorAll('.user-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 设置当前选中
            cardElement.classList.add('selected');
            document.getElementById('username').value = username;
        }
        
        // 测试登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            const loginBtn = document.getElementById('loginBtn');
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = '🔄 登录中...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userName: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const userInfo = data.data.userInfo;
                    const result = `✅ 登录成功！
                    
用户信息：
- 用户名: ${userInfo.username}
- 角色: ${userInfo.roleDisplay}
- 区域: ${userInfo.regionName || '无'}
- 区域代码: ${userInfo.regionCode || '无'}
- 用户ID: ${userInfo.id}

Token信息：
- Access Token: ${data.data.token.substring(0, 50)}...
- Refresh Token: ${data.data.refreshToken.substring(0, 50)}...

响应状态: ${response.status}
响应消息: ${data.msg}`;
                    
                    showResult(result, 'success');
                } else {
                    const result = `❌ 登录失败！
                    
错误信息: ${data.msg || '未知错误'}
响应状态: ${response.status}
错误代码: ${data.code || 'N/A'}

完整响应:
${JSON.stringify(data, null, 2)}`;
                    
                    showResult(result, 'error');
                }
                
            } catch (error) {
                showResult(`❌ 请求异常: ${error.message}`, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '🚀 测试登录';
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // 清空结果
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
        }
        
        // 页面加载时生成用户卡片
        document.addEventListener('DOMContentLoaded', generateUserCards);
        
        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testLogin();
            }
        });
    </script>
</body>
</html>
