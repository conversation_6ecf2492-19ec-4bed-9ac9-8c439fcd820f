"""
Core应用Celery任务相关视图
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

# 只有在Celery可用时才导入任务
try:
    from .tasks import (
        send_notification_email,
        cleanup_old_logs,
        sync_user_permissions,
        generate_system_report
    )
    from business.tasks import (
        sync_amazon_inventory,
        process_pending_orders,
        backup_business_data,
        send_daily_summary
    )
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_email_task(request):
    """
    触发发送邮件任务
    """
    if not CELERY_AVAILABLE:
        return Response({
            'success': False,
            'error': 'Celery未配置或不可用'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    try:
        subject = request.data.get('subject', '系统通知')
        message = request.data.get('message', '这是一条测试消息')
        recipients = request.data.get('recipients', ['<EMAIL>'])
        
        # 异步执行任务
        task = send_notification_email.delay(subject, message, recipients)
        
        return Response({
            'success': True,
            'task_id': task.id,
            'message': '邮件发送任务已提交'
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_system_tasks(request):
    """
    触发系统维护任务
    """
    if not CELERY_AVAILABLE:
        return Response({
            'success': False,
            'error': 'Celery未配置或不可用'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    try:
        task_type = request.data.get('task_type')
        
        if task_type == 'cleanup_logs':
            task = cleanup_old_logs.delay()
        elif task_type == 'sync_permissions':
            task = sync_user_permissions.delay()
        elif task_type == 'generate_report':
            task = generate_system_report.delay()
        else:
            return Response({
                'success': False,
                'error': '无效的任务类型'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': True,
            'task_id': task.id,
            'message': f'{task_type} 任务已提交'
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_business_tasks(request):
    """
    触发业务相关任务
    """
    if not CELERY_AVAILABLE:
        return Response({
            'success': False,
            'error': 'Celery未配置或不可用'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    try:
        task_type = request.data.get('task_type')
        
        if task_type == 'sync_inventory':
            task = sync_amazon_inventory.delay()
        elif task_type == 'process_orders':
            task = process_pending_orders.delay()
        elif task_type == 'backup_data':
            task = backup_business_data.delay()
        elif task_type == 'daily_summary':
            task = send_daily_summary.delay()
        else:
            return Response({
                'success': False,
                'error': '无效的任务类型'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': True,
            'task_id': task.id,
            'message': f'{task_type} 任务已提交'
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_task_status(request, task_id):
    """
    获取任务状态
    """
    if not CELERY_AVAILABLE:
        return Response({
            'success': False,
            'error': 'Celery未配置或不可用'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    try:
        from celery.result import AsyncResult
        
        task_result = AsyncResult(task_id)
        
        return Response({
            'task_id': task_id,
            'status': task_result.status,
            'result': task_result.result if task_result.ready() else None,
            'info': task_result.info
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)