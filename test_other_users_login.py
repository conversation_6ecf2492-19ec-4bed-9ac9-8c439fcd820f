#!/usr/bin/env python3
"""
测试其他用户的登录情况
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_user_login(username, password):
    """测试单个用户登录"""
    print(f"\n🔐 测试用户登录: {username}")
    
    login_data = {
        "userName": username,
        "password": password
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功: {data['msg']}")
            user_info = data['data']['userInfo']
            print(f"   - 用户: {user_info['username']}")
            print(f"   - 角色: {user_info['roleDisplay']}")
            print(f"   - 区域: {user_info.get('regionName', 'None')}")
            return True
        else:
            data = response.json()
            print(f"❌ 登录失败: {data.get('msg', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def main():
    """测试多个用户登录"""
    print("🚀 开始测试其他用户登录...")
    
    # 测试用户列表 - 使用常见的默认密码
    test_users = [
        ("super", "admin123"),  # 已知可以登录
        ("mpr_admin", "admin123"),
        ("mpr_admin", "123456"),
        ("rl_admin", "admin123"),
        ("rl_admin", "123456"),
        ("eo_admin", "admin123"),
        ("eo_admin", "123456"),
        ("zz_admin", "admin123"),
        ("zz_admin", "123456"),
        ("wh_admin", "admin123"),
        ("wh_admin", "123456"),
        ("MM", "admin123"),
        ("MM", "123456"),
        ("zhs", "admin123"),
        ("zhs", "123456"),
    ]
    
    success_count = 0
    total_count = len(test_users)
    
    for username, password in test_users:
        if test_user_login(username, password):
            success_count += 1
    
    print(f"\n📊 测试结果总结:")
    print(f"  - 总测试数: {total_count}")
    print(f"  - 成功登录: {success_count}")
    print(f"  - 失败登录: {total_count - success_count}")
    
    if success_count == 1:  # 只有super用户成功
        print("\n⚠️ 只有super用户可以登录，其他用户都失败了")
        print("可能的原因：")
        print("1. 其他用户的密码不是admin123或123456")
        print("2. 用户状态被禁用")
        print("3. 认证逻辑有问题")

if __name__ == "__main__":
    main()
