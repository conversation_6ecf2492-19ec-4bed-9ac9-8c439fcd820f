#!/usr/bin/env python3
"""
为开发者用户分配权限
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('ep_system')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
django.setup()

from core.models import CustomUser, Permission, UserPermission

def assign_developer_permissions():
    """为开发者用户分配所有开发者权限"""
    print("🔐 为开发者用户分配权限...")
    
    try:
        # 获取开发者用户
        developer_user = CustomUser.objects.unfiltered().get(username='developer')
        print(f"✅ 找到开发者用户: {developer_user.username}")
        
        # 获取所有开发者相关权限
        developer_permission_codes = [
            # 开发者权限
            'system_development',
            'api_management', 
            'database_management',
            'system_monitoring',
            'testing_tools',
            
            # 亚马逊API权限
            'amazon_api_config',
            'amazon_data_sync',
            'amazon_report_management',
            'amazon_ads_management',
            
            # 基础业务权限（用于测试）
            'warehouse_management',
            'sku_management',
            'shipping_management',
            'customer_management',
            'reports_view',
            
            # 系统管理权限
            'system_admin',
            'user_management',
        ]
        
        # 清除现有权限
        UserPermission.objects.filter(user=developer_user).delete()
        print("✅ 清除现有权限")
        
        # 分配新权限
        assigned_count = 0
        for perm_code in developer_permission_codes:
            try:
                permission = Permission.objects.get(code=perm_code)
                UserPermission.objects.create(user=developer_user, permission=permission)
                assigned_count += 1
                print(f"  ✅ {permission.name} ({perm_code})")
            except Permission.DoesNotExist:
                print(f"  ⚠️ 权限不存在: {perm_code}")
        
        print(f"\n📊 权限分配完成，共分配 {assigned_count} 个权限")
        
        # 验证权限分配
        user_permissions = UserPermission.objects.filter(user=developer_user).count()
        print(f"✅ 验证：开发者用户现有 {user_permissions} 个权限")
        
    except CustomUser.DoesNotExist:
        print("❌ 开发者用户不存在，请先运行 create_developer_menu_system.py")
    except Exception as e:
        print(f"❌ 分配权限失败: {e}")

def test_developer_login():
    """测试开发者登录"""
    print("\n🧪 测试开发者登录...")
    
    import requests
    
    base_url = "http://127.0.0.1:8000"
    login_data = {
        "userName": "developer",
        "password": "dev123456"
    }
    
    try:
        print("正在测试开发者登录...")
        login_response = requests.post(f"{base_url}/api/v1/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get('code') == 200:
                print("✅ 开发者登录成功")
                
                # 获取菜单
                access_token = login_result['data']['token']
                headers = {'Authorization': access_token}
                
                menu_response = requests.get(f"{base_url}/api/v1/menu/list/", headers=headers)
                if menu_response.status_code == 200:
                    menu_result = menu_response.json()
                    if menu_result.get('code') == 200:
                        menu_list = menu_result['data']['menuList']
                        menu_names = [menu['name'] for menu in menu_list]
                        
                        print(f"✅ 开发者可访问菜单 ({len(menu_list)}个): {', '.join(menu_names)}")
                        
                        # 检查是否有开发者控制台
                        if "开发者控制台" in menu_names:
                            print("✅ 开发者控制台菜单显示正常")
                            
                            # 查找开发者控制台的子菜单
                            for menu in menu_list:
                                if menu['name'] == '开发者控制台':
                                    children = menu.get('children', [])
                                    print(f"✅ 开发者控制台包含 {len(children)} 个子菜单")
                                    if children:
                                        child_names = [child['name'] for child in children[:5]]
                                        print(f"   前5个子菜单: {', '.join(child_names)}")
                                    break
                        else:
                            print("⚠️ 开发者控制台菜单未显示")
                        
                        return True
                
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("🚀 开始为开发者分配权限...")
    
    # 分配权限
    assign_developer_permissions()
    
    # 测试登录
    test_developer_login()
    
    print("\n🎉 开发者权限配置完成！")
    print("\n📝 开发者账户信息:")
    print("  用户名: developer")
    print("  密码: dev123456")
    print("  权限: 完整的开发者权限 + 系统管理权限")
    print("\n🛠️ 可用功能:")
    print("  • 开发者控制台 (17个子功能)")
    print("  • 亚马逊API管理")
    print("  • 系统监控和调试")
    print("  • 数据库管理工具")
    print("  • 测试工具集")
    print("  • 所有业务功能 (用于测试)")

if __name__ == "__main__":
    main()
