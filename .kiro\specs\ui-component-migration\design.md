# Design Document

## Overview

本设计文档详细规划了将项目中两个开发者工具页面（database-tools 和 amazon-config）从 ant-design-vue 组件迁移到 element-plus 组件的技术方案。迁移将采用组件一对一映射的方式，确保功能和视觉效果的一致性。

## Architecture

### 迁移策略
- **渐进式迁移**：逐个文件进行迁移，确保每个文件迁移完成后都能正常工作
- **组件映射**：建立 ant-design-vue 到 element-plus 的组件映射关系
- **样式保持**：通过自定义CSS确保迁移后的视觉效果与原设计一致
- **功能验证**：每个组件迁移后都进行功能测试

### 受影响的文件
1. `art/src/views/developer/database-tools/index.vue`
2. `art/src/views/developer/amazon-config/index.vue`

## Components and Interfaces

### 组件映射表

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-card` | `el-card` | 卡片组件 |
| `a-button` | `el-button` | 按钮组件 |
| `a-row` | `el-row` | 行布局 |
| `a-col` | `el-col` | 列布局 |
| `a-select` | `el-select` | 选择器 |
| `a-select-option` | `el-option` | 选择器选项 |
| `a-textarea` | `el-input` (type="textarea") | 文本域 |
| `a-table` | `el-table` | 表格组件 |
| `a-modal` | `el-dialog` | 对话框 |
| `a-tabs` | `el-tabs` | 标签页 |
| `a-tab-pane` | `el-tab-pane` | 标签页面板 |
| `a-space` | `el-space` | 间距组件 |
| `a-alert` | `el-alert` | 警告提示 |
| `a-badge` | `el-badge` | 徽章 |

### 导入语句迁移

**原导入 (ant-design-vue):**
```javascript
import { message } from 'ant-design-vue'
import { PlayCircleOutlined, ClearOutlined, TableOutlined, DownloadOutlined } from '@ant-design/icons-vue'
```

**新导入 (element-plus):**
```javascript
import { ElMessage } from 'element-plus'
import { Play, Delete, Grid, Download } from '@element-plus/icons-vue'
```

### 属性映射

#### 按钮组件
- `loading` → `loading` (相同)
- `type="primary"` → `type="primary"` (相同)
- `size="small"` → `size="small"` (相同)

#### 表格组件
- `:columns` → 需要重构为 `el-table-column` 组件
- `:data-source` → `:data`
- `:pagination` → 需要单独的 `el-pagination` 组件

#### 对话框组件
- `v-model:open` → `v-model`
- `:footer="null"` → 通过插槽控制

## Data Models

### 表格列配置重构

**原 ant-design-vue 方式:**
```javascript
const columns = [
  { title: '表名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' }
]
```

**新 element-plus 方式:**
```vue
<el-table-column prop="name" label="表名" />
<el-table-column prop="type" label="类型" />
```

### 消息提示重构

**原方式:**
```javascript
message.success('操作成功')
message.warning('警告信息')
message.error('错误信息')
```

**新方式:**
```javascript
ElMessage.success('操作成功')
ElMessage.warning('警告信息')
ElMessage.error('错误信息')
```

## Error Handling

### 迁移过程中的错误处理
1. **组件属性不匹配**：建立属性映射表，确保所有属性正确转换
2. **样式差异**：通过自定义CSS类调整样式，保持视觉一致性
3. **事件处理差异**：验证所有事件处理器在新组件中正常工作
4. **图标缺失**：确保所有图标都有对应的 element-plus 图标

### 运行时错误预防
1. **类型检查**：确保所有 TypeScript 类型定义正确
2. **属性验证**：验证所有组件属性都符合 element-plus 规范
3. **事件绑定**：确保所有事件处理器正确绑定

## Testing Strategy

### 功能测试
1. **数据库工具页面测试**
   - SQL查询编辑器功能
   - 查询结果显示
   - 表结构查看
   - 数据导出功能
   - 查询模板使用

2. **Amazon配置页面测试**
   - API配置功能
   - 连接测试
   - 密钥管理
   - 区域切换

### 视觉测试
1. **布局一致性**：确保页面布局与原设计一致
2. **组件样式**：验证所有组件的视觉效果
3. **响应式设计**：测试不同屏幕尺寸下的显示效果

### 兼容性测试
1. **浏览器兼容性**：在主流浏览器中测试
2. **功能完整性**：确保所有原有功能都正常工作
3. **性能测试**：验证页面加载和交互性能

## Implementation Notes

### 迁移顺序
1. 先迁移 `database-tools/index.vue`（组件使用更复杂）
2. 再迁移 `amazon-config/index.vue`
3. 最后进行整体测试和优化

### 样式调整策略
- 保持原有的自定义CSS类
- 必要时添加新的CSS规则来匹配 element-plus 组件的默认样式
- 使用CSS变量来保持主题一致性

### 代码质量保证
- 保持原有的代码结构和逻辑
- 添加必要的注释说明迁移的变更
- 确保代码符合项目的编码规范