<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="60%"
    align-center
    :close-on-click-modal="false"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="135px">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="登录名" prop="username">
            <ElInput v-model="formData.username" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="姓名" prop="memberProfile.real_name">
            <ElInput v-model="formData.memberProfile.real_name" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 角色选择 - 只在新增用户时显示 -->
      <ElRow :gutter="20" v-if="dialogType === 'add'">
        <ElCol :span="24">
          <ElFormItem label="角色" prop="role">
            <ElSelect v-model="formData.role" @change="handleRoleChange" style="width: 100%">
              <ElOption
                v-for="role in roleOptions"
                :key="role.value"
                :value="role.value"
                :label="role.label"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 编辑模式下显示当前角色信息 -->
      <ElRow :gutter="20" v-if="dialogType === 'edit'">
        <ElCol :span="24">
          <ElFormItem label="当前角色">
            <ElInput
              :value="getRoleDisplayName(formData.role)"
              readonly
              disabled
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 会员专用字段 -->
      <template v-if="formData.role === 'MEMBER'">
        <!-- 基础信息 -->
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="密码" prop="password">
              <ElInput
                v-model="formData.password"
                type="password"
                placeholder="留空则使用默认密码"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="英文名" prop="memberProfile.english_name">
              <ElInput v-model="formData.memberProfile.english_name" placeholder="用于FBA发票" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="电话" prop="phone">
              <ElInput v-model="formData.phone" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="说明" prop="memberProfile.description">
              <ElInput
                v-model="formData.memberProfile.description"
                placeholder="可填写地区等其它信息"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="图片服务器地址" prop="memberProfile.image_server_url">
              <ElInput
                v-model="formData.memberProfile.image_server_url"
                placeholder="请输入图片服务器地址"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="文档服务器地址" prop="memberProfile.document_server_url">
              <ElInput
                v-model="formData.memberProfile.document_server_url"
                placeholder="请输入文档服务器地址"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="财务AuthToken" prop="memberProfile.finance_auth_token">
              <ElInput
                v-model="formData.memberProfile.finance_auth_token"
                type="password"
                show-password
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="SKU前缀" prop="memberProfile.sku_prefix">
              <ElInput v-model="formData.memberProfile.sku_prefix" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="EMS大客户号" prop="memberProfile.ems_vip_number">
              <ElInput v-model="formData.memberProfile.ems_vip_number" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="EMS客户代码" prop="memberProfile.ems_customer_code">
              <ElInput v-model="formData.memberProfile.ems_customer_code" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="EMS版本信息" prop="memberProfile.ems_version_info">
              <ElInput v-model="formData.memberProfile.ems_version_info" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="EMS AuthToken号" prop="memberProfile.ems_auth_token">
              <ElInput v-model="formData.memberProfile.ems_auth_token" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="亚马逊用户名" prop="memberProfile.amazon_username">
              <ElInput v-model="formData.memberProfile.amazon_username" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="亚马逊Seller ID" prop="memberProfile.amazon_seller_id">
              <ElInput v-model="formData.memberProfile.amazon_seller_id" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="授权令牌" prop="memberProfile.mws_auth_token">
              <ElInput
                v-model="formData.memberProfile.mws_auth_token"
                type="password"
                show-password
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品所在仓库" prop="memberProfile.warehouse_location">
              <ElSelect
                v-model="formData.memberProfile.warehouse_location"
                placeholder="请选择仓库"
                style="width: 100%"
              >
                <ElOption value="富民路三楼仓库" label="富民路三楼仓库" />
                <ElOption value="富民路二楼仓库" label="富民路二楼仓库" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="支付渠道" prop="memberProfile.status">
              <ElRadioGroup v-model="formData.memberProfile.status">
                <ElRadio value="kjb">跨境宝</ElRadio>
                <ElRadio value="other">支付宝及其他</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="支付渠道" prop="memberProfile.payment_channel">
              <ElInput v-model="formData.memberProfile.payment_channel" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="父账号" prop="memberProfile.parent_account">
              <ElSelect
                v-model="formData.memberProfile.parent_account"
                placeholder="请选择父账号"
                clearable
                style="width: 100%"
              >
                <ElOption
                  v-for="member in memberList"
                  :key="member.id"
                  :value="member.username"
                  :label="member.username"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="豁免品牌" prop="memberProfile.exempt_brands">
              <ElInput
                v-model="formData.memberProfile.exempt_brands"
                placeholder="多个品牌用逗号分隔"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </template>

      <!-- 助理专用字段 -->
      <template v-if="isAssistantRole(formData.role)">
        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="负责会员" prop="responsibleMemberIds">
              <ElSelect
                v-model="formData.responsibleMemberIds"
                multiple
                placeholder="请选择负责的会员（可选）"
                clearable
                style="width: 100%"
              >
                <ElOption
                  v-for="member in memberList"
                  :key="member.id"
                  :value="member.id"
                  :label="`${member.username} (${member.phone || ''})`"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="说明" prop="assistantProfile.description">
              <ElInput
                v-model="formData.assistantProfile.description"
                type="textarea"
                placeholder="可填写地区等其它信息"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem
              label="关联财务助理AuthToken"
              prop="assistantProfile.related_finance_auth_token"
            >
              <ElInput
                v-model="formData.assistantProfile.related_finance_auth_token"
                type="password"
                show-password
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12" v-if="formData.role === 'MEMBER_ASSISTANT'">
            <ElFormItem
              label="内部会员财务AuthToken"
              prop="assistantProfile.internal_member_finance_auth_token"
            >
              <ElInput
                v-model="formData.assistantProfile.internal_member_finance_auth_token"
                type="password"
                show-password
                placeholder="内部会员的助理才有此项"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </template>

      <ElFormItem
        v-if="dialogType === 'add' && formData.role !== 'MEMBER'"
        label="初始密码"
        prop="password"
      >
        <ElInput v-model="formData.password" type="password" placeholder="留空则使用默认密码" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">提交</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { UserService } from '@/api/usersApi'
  import { useUserStore } from '@/store/modules/user'
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElCol, ElMessage, ElRadio, ElRadioGroup, ElRow } from 'element-plus'
  import { computed, nextTick, reactive, ref, watch } from 'vue'

  interface Props {
    visible: boolean
    type: string
    userData?: any
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 获取用户store
  const userStore = useUserStore()

  // 角色创建权限映射
  const ROLE_CREATION_PERMISSIONS: Record<string, string[]> = {
    SUPER_ADMIN: ['REGION_ADMIN', 'FINANCE_STAFF'],
    REGION_ADMIN: [
      'MEMBER',
      'MEMBER_ASSISTANT',
      'TRANSLATOR_ASSISTANT',
      'DOCUMENT_ASSISTANT',
      'WAREHOUSE_MANAGER'
    ]
  }

  // 所有角色选项
  const allRoleOptions = [
    { value: 'SUPER_ADMIN', label: '超级管理员' },
    { value: 'REGION_ADMIN', label: '区域管理员' },
    { value: 'MEMBER', label: '普通会员' },
    { value: 'MEMBER_ASSISTANT', label: '会员助理' },
    { value: 'TRANSLATOR_ASSISTANT', label: '翻译助理' },
    { value: 'DOCUMENT_ASSISTANT', label: '文档助理' },
    { value: 'OPERATION_SPECIALIST', label: '精细化操作人员' },
    { value: 'FINANCE_STAFF', label: '财务人员' },
    { value: 'WAREHOUSE_MANAGER', label: '库管' }
  ]

  // 根据当前用户角色过滤可创建的角色选项
  const roleOptions = computed(() => {
    const currentUserRole = userStore.info?.role
    if (!currentUserRole) return []

    const allowedRoles = ROLE_CREATION_PERMISSIONS[currentUserRole] || []
    return allRoleOptions.filter((option) => allowedRoles.includes(option.value))
  })

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    username: '',
    phone: '',
    role: '',
    password: '',
    managedById: undefined as number | undefined,

    responsibleMemberIds: [] as number[],
    // 会员Profile数据
    memberProfile: {
      real_name: '',
      english_name: '',
      description: '',
      image_server_url: '',
      document_server_url: '',
      finance_auth_token: '',
      sku_prefix: '',
      payment_channel: '',
      parent_account: '',
      ems_vip_number: '',
      ems_customer_code: '',
      ems_version_info: 'international_eub_us_1.1',
      international_eub_us: '',
      ems_auth_token: 'bsbios_64a4b0dd5d5d394eb7a8c00171fca51f',
      amazon_username: '',
      amazon_seller_id: '',
      amazon_marketplace_id: '',
      aws_access_key_id: '',
      aws_secret_key: '',
      merchant_token: '',
      developer_aws_access: '',
      developer_secret_key: '',
      mws_auth_token: '',
      warehouse_location: '',
      status: 'kjb',
      exempt_brands: ''
    },
    // 助理Profile数据
    assistantProfile: {
      description: '',
      related_finance_auth_token: '',
      internal_member_finance_auth_token: ''
    }
  })

  // 助理列表
  const assistantList = ref<Api.Common.AssistantInfo[]>([])

  // 会员列表
  const memberList = ref<Api.Common.MemberInfo[]>([])

  // 辅助函数：判断是否是助理角色
  const isAssistantRole = (role: string) => {
    return ['MEMBER_ASSISTANT', 'TRANSLATOR_ASSISTANT', 'DOCUMENT_ASSISTANT'].includes(role)
  }

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    const roleOption = allRoleOptions.find((option) => option.value === role)
    return roleOption ? roleOption.label : role
  }

  // 获取助理列表
  const getAssistantList = async () => {
    try {
      const result = await UserService.getAssistantList()
      assistantList.value = result
    } catch (error) {
      console.error('获取助理列表失败:', error)
    }
  }

  // 获取会员列表
  const getMemberList = async () => {
    try {
      const result = await UserService.getMemberList()
      memberList.value = result
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  // 角色变化处理
  const handleRoleChange = (role: string) => {
    // 清空相关字段
    formData.managedById = undefined
    formData.responsibleMemberIds = []

    if (role === 'MEMBER') {
      // 当选择会员角色时，获取会员列表用于父账号选择
      getMemberList()
    } else if (role === 'MEMBER_ASSISTANT') {
      // 当选择助理角色时，获取会员列表
      getMemberList()
    }
  }

  // 表单验证规则
  const rules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    phone: [
      { required: false, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    role: [{ required: true, message: '请选择角色', trigger: 'blur' }],

    // 会员Profile验证规则
    'memberProfile.english_name': [
      { max: 100, message: '英文名长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.image_server_url': [
      { max: 200, message: '图片服务器地址长度不能超过200个字符', trigger: 'blur' }
    ],
    'memberProfile.document_server_url': [
      { max: 200, message: '文档服务器地址长度不能超过200个字符', trigger: 'blur' }
    ],
    'memberProfile.sku_prefix': [
      { max: 50, message: 'SKU前缀长度不能超过50个字符', trigger: 'blur' }
    ],
    'memberProfile.payment_channel': [
      { max: 100, message: '支付渠道长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.parent_account': [
      { max: 100, message: '父账号长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.ems_vip_number': [
      { max: 100, message: 'EMS大客户号长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.ems_customer_code': [
      { max: 100, message: 'EMS客户代码长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_username': [
      { max: 100, message: '亚马逊用户名长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_seller_id': [
      { max: 100, message: '亚马逊Seller ID长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_marketplace_id': [
      { max: 100, message: '亚马逊Marketplace ID长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.warehouse_location': [
      { max: 100, message: '产品所在仓库长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.status': [{ required: true, message: '请选择支付渠道', trigger: 'change' }],

    // 助理Profile验证规则
    'assistantProfile.description': [
      { max: 500, message: '说明长度不能超过500个字符', trigger: 'blur' }
    ]
  }

  // 初始化表单数据
  const initFormData = async () => {
    const isEdit = props.type === 'edit' && props.userData
    const row = props.userData

    Object.assign(formData, {
      username: isEdit ? row.userName || '' : '',
      phone: isEdit ? row.userPhone || '' : '',
      role: isEdit ? row.role || '' : '',
      password: '',
      managedById: undefined,
      responsibleMemberIds: []
    })

    // 编辑模式下加载用户详情
    if (isEdit && row.id) {
      try {
        const userData = await UserService.getUserDetail(row.id)

        // 更新表单数据
        Object.assign(formData, {
          username: userData.username,
          phone: userData.phone,
          role: userData.role,
          managedById: userData.managedBy?.id || null,
          responsibleMemberIds: userData.responsibleMembers.map((m) => m.id)
        })

        // 更新Profile数据
        if (userData.role === 'MEMBER' && userData.member_profile) {
          Object.assign(formData.memberProfile, userData.member_profile)
        } else if (isAssistantRole(userData.role) && userData.assistant_profile) {
          Object.assign(formData.assistantProfile, userData.assistant_profile)
        }
      } catch (error) {
        console.error('加载用户详情失败:', error)
        ElMessage.error('加载用户详情失败')
      }
    }

    // 根据角色初始化相关数据
    if (formData.role === 'MEMBER') {
      getAssistantList()
    } else if (formData.role === 'MEMBER_ASSISTANT') {
      getMemberList()
    }
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    ([visible]) => {
      if (visible) {
        initFormData()
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      if (dialogType.value === 'add') {
        // 新增用户
        const params: any = {
          username: formData.username,
          phone: formData.phone,
          role: formData.role,
          password: formData.password || undefined,
          managed_by_id: formData.role === 'MEMBER' ? formData.managedById || undefined : undefined,

          responsible_member_ids: isAssistantRole(formData.role)
            ? formData.responsibleMemberIds
            : undefined
        }

        // 添加Profile数据
        if (formData.role === 'MEMBER') {
          params.member_profile = formData.memberProfile
        } else if (isAssistantRole(formData.role)) {
          params.assistant_profile = formData.assistantProfile
        }

        const result = await UserService.createUser(params)
        ElMessage.success(`用户创建成功！默认密码：${result.defaultPassword}`)
      } else {
        // 更新用户
        const params: any = {
          username: formData.username,
          phone: formData.phone,
          role: formData.role,

          responsible_member_ids: isAssistantRole(formData.role)
            ? formData.responsibleMemberIds
            : undefined
        }

        // 添加Profile数据
        if (formData.role === 'MEMBER') {
          params.member_profile = formData.memberProfile
        } else if (isAssistantRole(formData.role)) {
          params.assistant_profile = formData.assistantProfile
        }

        await UserService.updateUser(props.userData.id, params)
        ElMessage.success('用户更新成功')
      }

      dialogVisible.value = false
      emit('submit')
    } catch (error: any) {
      console.error('提交失败:', error)
      ElMessage.error(error.message || '操作失败，请重试')
    }
  }
</script>
