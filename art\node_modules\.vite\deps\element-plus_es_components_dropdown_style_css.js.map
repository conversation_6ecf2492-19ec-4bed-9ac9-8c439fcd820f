{"version": 3, "sources": ["../../.pnpm/element-plus@2.9.11_vue@3.5.15_typescript@5.6.3_/node_modules/element-plus/es/components/dropdown/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport '../../button/style/css.mjs';\nimport '../../button-group/style/css.mjs';\nimport '../../popper/style/css.mjs';\nimport '../../scrollbar/style/css.mjs';\nimport 'element-plus/theme-chalk/el-dropdown.css';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";;;;;;;AAKA,OAAO;", "names": []}