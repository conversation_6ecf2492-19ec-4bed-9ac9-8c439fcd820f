"""
Core应用视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from .serializers import (
    LoginSerializer,
    LoginResponseSerializer,
    UserInfoSerializer,
    MenuSerializer
)
from .models import Menu

# 临时的基础视图类，确保Django可以正常启动
class RegistrationAPIView(APIView):
    """用户注册API"""
    def post(self, request):
        return Response({'message': '注册功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class LoginAPIView(APIView):
    """用户登录API"""
    def post(self, request):
        """处理登录请求"""
        serializer = LoginSerializer(data=request.data)

        if not serializer.is_valid():
            return Response({
                'code': status.HTTP_400_BAD_REQUEST,
                'msg': serializer.errors.get('non_field_errors', ['用户名或密码错误'])[0],
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取验证后的用户
        user = serializer.validated_data['user']

        # 创建JWT令牌
        tokens = serializer.create_tokens(user)

        # 更新用户最后登录时间
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])

        # 保存refresh token到用户模型
        user.refresh_token = tokens['refresh']
        user.save(update_fields=['refresh_token'])

        # 序列化用户信息
        user_info = UserInfoSerializer(user).data

        # 构建响应数据
        response_data = {
            'token': tokens['access'],
            'refreshToken': tokens['refresh'],
            'userInfo': user_info
        }

        # 返回成功响应
        return Response({
            'code': status.HTTP_200_OK,
            'msg': '登录成功',
            'data': response_data
        }, status=status.HTTP_200_OK)

class UserInfoAPIView(APIView):
    """用户信息API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前用户信息"""
        try:
            user = request.user

            # 序列化用户信息
            user_info = UserInfoSerializer(user).data

            return Response({
                'code': status.HTTP_200_OK,
                'msg': '获取用户信息成功',
                'data': user_info
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'msg': f'获取用户信息失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserListAPIView(APIView):
    """用户列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '用户列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class AssistantListAPIView(APIView):
    """助理列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '助理列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class UserDetailAPIView(APIView):
    """用户详情API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id):
        return Response({'message': '用户详情功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class UserRefreshTokenAPIView(APIView):
    """用户刷新令牌API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, user_id):
        return Response({'message': '刷新令牌功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class DomainInfoAPIView(APIView):
    """域信息API"""
    def get(self, request):
        return Response({'message': '域信息功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class PermissionListAPIView(APIView):
    """权限列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '权限列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class MemberListAPIView(APIView):
    """成员列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '成员列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class MenuAPIView(APIView):
    """菜单API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户菜单列表"""
        try:
            user = request.user

            # 根据用户角色生成菜单数据
            menu_data = self.generate_menu_for_user(user)

            return Response({
                'code': status.HTTP_200_OK,
                'msg': '获取菜单成功',
                'data': {
                    'menuList': menu_data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'msg': f'获取菜单失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def generate_menu_for_user(self, user):
        """根据用户角色生成菜单数据"""
        # 基础菜单结构
        base_menus = [
            {
                'id': 1,
                'name': 'Dashboard',
                'path': '/dashboard',
                'component': 'Layout',
                'meta': {
                    'title': '仪表盘',
                    'icon': 'dashboard',
                    'order': 1
                },
                'children': [
                    {
                        'id': 11,
                        'name': 'Analysis',
                        'path': '/dashboard/analysis',
                        'component': 'views/dashboard/analysis/index',
                        'meta': {
                            'title': '分析页',
                            'icon': 'analysis'
                        }
                    }
                ]
            }
        ]

        # 根据用户角色添加不同的菜单
        if user.role == 'SUPER_ADMIN':
            # 超级管理员拥有所有菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'System',
                    'path': '/system',
                    'component': 'Layout',
                    'meta': {
                        'title': '系统管理',
                        'icon': 'system',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'User',
                            'path': '/system/user',
                            'component': 'views/system/user/index',
                            'meta': {
                                'title': '用户管理',
                                'icon': 'user'
                            }
                        },
                        {
                            'id': 22,
                            'name': 'Role',
                            'path': '/system/role',
                            'component': 'views/system/role/index',
                            'meta': {
                                'title': '角色管理',
                                'icon': 'role'
                            }
                        }
                    ]
                },
                {
                    'id': 3,
                    'name': 'Business',
                    'path': '/business',
                    'component': 'Layout',
                    'meta': {
                        'title': '业务管理',
                        'icon': 'business',
                        'order': 3
                    },
                    'children': [
                        {
                            'id': 31,
                            'name': 'SKU',
                            'path': '/business/sku',
                            'component': 'views/business/sku/index',
                            'meta': {
                                'title': 'SKU管理',
                                'icon': 'sku'
                            }
                        }
                    ]
                }
            ])
        elif user.role == 'REGION_ADMIN':
            # 区域管理员菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'Management',
                    'path': '/management',
                    'component': 'Layout',
                    'meta': {
                        'title': '区域管理',
                        'icon': 'management',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'Member',
                            'path': '/management/member',
                            'component': 'views/management/member/index',
                            'meta': {
                                'title': '成员管理',
                                'icon': 'member'
                            }
                        }
                    ]
                }
            ])
        elif user.role == 'MEMBER':
            # 普通会员菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'MyBusiness',
                    'path': '/my-business',
                    'component': 'Layout',
                    'meta': {
                        'title': '我的业务',
                        'icon': 'business',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'MySKU',
                            'path': '/my-business/sku',
                            'component': 'views/business/sku/index',
                            'meta': {
                                'title': '我的SKU',
                                'icon': 'sku'
                            }
                        }
                    ]
                }
            ])

        return base_menus

    def post(self, request):
        """创建菜单"""
        return Response({
            'code': status.HTTP_501_NOT_IMPLEMENTED,
            'msg': '菜单创建功能待实现',
            'data': None
        }, status=status.HTTP_501_NOT_IMPLEMENTED)