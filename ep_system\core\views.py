"""
Core应用视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from .serializers import (
    LoginSerializer,
    LoginResponseSerializer,
    UserInfoSerializer,
    MenuSerializer
)
from .models import Menu, CustomUser

# 临时的基础视图类，确保Django可以正常启动
class RegistrationAPIView(APIView):
    """用户注册API"""
    def post(self, request):
        return Response({'message': '注册功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class LoginAPIView(APIView):
    """用户登录API"""
    def post(self, request):
        """处理登录请求"""
        serializer = LoginSerializer(data=request.data)

        if not serializer.is_valid():
            return Response({
                'code': status.HTTP_400_BAD_REQUEST,
                'msg': serializer.errors.get('non_field_errors', ['用户名或密码错误'])[0],
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取验证后的用户
        user = serializer.validated_data['user']

        # 创建JWT令牌
        tokens = serializer.create_tokens(user)

        # 更新用户最后登录时间
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])

        # 保存refresh token到用户模型
        user.refresh_token = tokens['refresh']
        user.save(update_fields=['refresh_token'])

        # 序列化用户信息
        user_info = UserInfoSerializer(user).data

        # 构建响应数据
        response_data = {
            'token': tokens['access'],
            'refreshToken': tokens['refresh'],
            'userInfo': user_info
        }

        # 返回成功响应
        return Response({
            'code': status.HTTP_200_OK,
            'msg': '登录成功',
            'data': response_data
        }, status=status.HTTP_200_OK)

class UserInfoAPIView(APIView):
    """用户信息API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前用户信息"""
        try:
            user = request.user

            # 序列化用户信息
            user_info = UserInfoSerializer(user).data

            return Response({
                'code': status.HTTP_200_OK,
                'msg': '获取用户信息成功',
                'data': user_info
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'msg': f'获取用户信息失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserListAPIView(APIView):
    """用户列表API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户列表"""
        try:
            # 获取查询参数
            current = int(request.GET.get('current', 1))
            size = int(request.GET.get('size', 10))
            name = request.GET.get('name', '')
            role = request.GET.get('role', '')
            is_active = request.GET.get('isActive', '')

            # 构建查询集
            queryset = CustomUser.objects.all()

            # 应用过滤条件
            if name:
                queryset = queryset.filter(username__icontains=name)
            if role:
                queryset = queryset.filter(role=role)
            if is_active:
                is_active_bool = is_active.lower() == 'true'
                queryset = queryset.filter(is_active=is_active_bool)

            # 计算总数
            total = queryset.count()

            # 分页
            start = (current - 1) * size
            end = start + size
            users = queryset[start:end]

            # 序列化用户数据
            user_list = []
            for user in users:
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'phone': user.phone,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                    'regionName': user.region.name if user.region else None,
                    'regionCode': user.region.code if user.region else None,
                    'isActive': user.is_active,
                    'dateJoined': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                    'lastLogin': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None,
                }
                user_list.append(user_data)

            return Response({
                'code': status.HTTP_200_OK,
                'msg': '获取用户列表成功',
                'data': {
                    'records': user_list,
                    'total': total,
                    'current': current,
                    'size': size
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'msg': f'获取用户列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AssistantListAPIView(APIView):
    """助理列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '助理列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class UserDetailAPIView(APIView):
    """用户详情API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id):
        return Response({'message': '用户详情功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class UserRefreshTokenAPIView(APIView):
    """用户刷新令牌API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, user_id):
        return Response({'message': '刷新令牌功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class DomainInfoAPIView(APIView):
    """域信息API"""
    def get(self, request):
        return Response({'message': '域信息功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class PermissionListAPIView(APIView):
    """权限列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '权限列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class MemberListAPIView(APIView):
    """成员列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        return Response({'message': '成员列表功能待实现'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class MenuAPIView(APIView):
    """菜单API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户菜单列表"""
        try:
            user = request.user

            # 从数据库获取菜单数据
            menu_data = self.get_menu_from_database(user)

            return Response({
                'code': status.HTTP_200_OK,
                'msg': '获取菜单成功',
                'data': {
                    'menuList': menu_data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'msg': f'获取菜单失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_menu_from_database(self, user):
        """从数据库获取菜单数据"""
        # 获取顶级菜单（没有父菜单的菜单）
        top_level_menus = Menu.objects.filter(parent__isnull=True).order_by('id')

        menu_data = []
        for menu in top_level_menus:
            # 检查用户是否有权限访问此菜单
            if self.user_has_menu_permission(user, menu):
                menu_item = {
                    'id': menu.id,
                    'name': menu.name,
                    'path': menu.path,
                    'component': menu.component,
                    'meta': menu.meta,
                    'children': self.get_menu_children(menu, user)
                }
                menu_data.append(menu_item)

        return menu_data

    def get_menu_children(self, parent_menu, user):
        """递归获取子菜单"""
        children = Menu.objects.filter(parent=parent_menu).order_by('id')
        children_data = []

        for child in children:
            if self.user_has_menu_permission(user, child):
                child_item = {
                    'id': child.id,
                    'name': child.name,
                    'path': child.path,
                    'component': child.component,
                    'meta': child.meta,
                    'children': self.get_menu_children(child, user)
                }
                children_data.append(child_item)

        return children_data

    def user_has_menu_permission(self, user, menu):
        """检查用户是否有权限访问菜单"""
        # 超级管理员可以访问所有菜单
        if user.role == 'SUPER_ADMIN':
            return True

        # 检查菜单的meta中是否有角色限制
        meta = menu.meta or {}
        required_roles = meta.get('roles', [])

        # 如果没有角色限制，默认允许访问
        if not required_roles:
            return True

        # 角色映射
        user_roles = []
        if user.role == 'SUPER_ADMIN':
            user_roles = ['R_SUPER', 'R_ADMIN']
        elif user.role == 'REGION_ADMIN':
            user_roles = ['R_ADMIN']
        elif user.role == 'MEMBER':
            user_roles = ['R_USER']
        elif user.role == 'MEMBER_ASSISTANT':
            user_roles = ['R_USER', 'R_ASSISTANT']
        elif user.role == 'WAREHOUSE_MANAGER':
            user_roles = ['R_USER', 'R_WAREHOUSE']

        # 检查用户角色是否满足菜单要求
        return any(role in required_roles for role in user_roles)

    def generate_menu_for_user(self, user):
        """根据用户角色生成菜单数据"""
        # 基础菜单结构
        base_menus = [
            {
                'id': 1,
                'name': 'Dashboard',
                'path': '/dashboard',
                'component': 'Layout',
                'meta': {
                    'title': '仪表盘',
                    'icon': 'dashboard',
                    'order': 1
                },
                'children': [
                    {
                        'id': 11,
                        'name': 'Analysis',
                        'path': '/dashboard/analysis',
                        'component': 'views/dashboard/analysis/index',
                        'meta': {
                            'title': '分析页',
                            'icon': 'analysis'
                        }
                    }
                ]
            }
        ]

        # 根据用户角色添加不同的菜单
        if user.role == 'SUPER_ADMIN':
            # 超级管理员拥有所有菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'System',
                    'path': '/system',
                    'component': 'Layout',
                    'meta': {
                        'title': '系统管理',
                        'icon': 'system',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'User',
                            'path': '/system/user',
                            'component': 'views/system/user/index',
                            'meta': {
                                'title': '用户管理',
                                'icon': 'user'
                            }
                        },
                        {
                            'id': 22,
                            'name': 'Role',
                            'path': '/system/role',
                            'component': 'views/system/role/index',
                            'meta': {
                                'title': '角色管理',
                                'icon': 'role'
                            }
                        }
                    ]
                },
                {
                    'id': 3,
                    'name': 'Business',
                    'path': '/business',
                    'component': 'Layout',
                    'meta': {
                        'title': '业务管理',
                        'icon': 'business',
                        'order': 3
                    },
                    'children': [
                        {
                            'id': 31,
                            'name': 'SKU',
                            'path': '/business/sku',
                            'component': 'views/business/sku/index',
                            'meta': {
                                'title': 'SKU管理',
                                'icon': 'sku'
                            }
                        }
                    ]
                }
            ])
        elif user.role == 'REGION_ADMIN':
            # 区域管理员菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'Management',
                    'path': '/management',
                    'component': 'Layout',
                    'meta': {
                        'title': '区域管理',
                        'icon': 'management',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'Member',
                            'path': '/management/member',
                            'component': 'views/management/member/index',
                            'meta': {
                                'title': '成员管理',
                                'icon': 'member'
                            }
                        }
                    ]
                }
            ])
        elif user.role == 'MEMBER':
            # 普通会员菜单
            base_menus.extend([
                {
                    'id': 2,
                    'name': 'MyBusiness',
                    'path': '/my-business',
                    'component': 'Layout',
                    'meta': {
                        'title': '我的业务',
                        'icon': 'business',
                        'order': 2
                    },
                    'children': [
                        {
                            'id': 21,
                            'name': 'MySKU',
                            'path': '/my-business/sku',
                            'component': 'views/business/sku/index',
                            'meta': {
                                'title': '我的SKU',
                                'icon': 'sku'
                            }
                        }
                    ]
                }
            ])

        return base_menus

    def post(self, request):
        """创建菜单"""
        return Response({
            'code': status.HTTP_501_NOT_IMPLEMENTED,
            'msg': '菜单创建功能待实现',
            'data': None
        }, status=status.HTTP_501_NOT_IMPLEMENTED)