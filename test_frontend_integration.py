#!/usr/bin/env python3
"""
测试前端集成功能
"""
import requests
import json
import time

def test_frontend_integration():
    """测试前端与后端的集成"""
    backend_url = "http://127.0.0.1:8000"
    frontend_url = "http://localhost:3008"
    
    print("🧪 开始测试前端后端集成...")
    
    # 1. 测试后端登录API
    print("\n1️⃣ 测试后端登录API...")
    login_data = {
        "userName": "super",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(
            f"{backend_url}/api/v1/auth/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('data', {}).get('token')
            print(f"✅ 后端登录成功，token: {token[:20]}...")
            
            # 2. 测试菜单API
            print("\n2️⃣ 测试菜单API...")
            menu_response = requests.get(
                f"{backend_url}/api/v1/menu/list/",
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
            )
            
            if menu_response.status_code == 200:
                menu_result = menu_response.json()
                menu_count = len(menu_result.get('data', {}).get('menuList', []))
                print(f"✅ 菜单API成功，获取到 {menu_count} 个菜单")
                
                # 3. 检查前端服务器状态
                print("\n3️⃣ 检查前端服务器状态...")
                try:
                    frontend_response = requests.get(frontend_url, timeout=5)
                    if frontend_response.status_code == 200:
                        print("✅ 前端服务器运行正常")
                        
                        print("\n🎉 集成测试完成！")
                        print("=" * 50)
                        print("✅ 后端登录API - 正常")
                        print("✅ 后端菜单API - 正常") 
                        print("✅ 前端服务器 - 正常")
                        print("=" * 50)
                        print(f"🌐 前端地址: {frontend_url}")
                        print(f"🔧 后端地址: {backend_url}")
                        print("\n💡 现在可以在浏览器中访问前端进行登录测试！")
                        
                    else:
                        print(f"❌ 前端服务器响应异常: {frontend_response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"❌ 前端服务器连接失败: {e}")
                    
            else:
                print(f"❌ 菜单API失败: {menu_response.status_code}")
                print(f"错误信息: {menu_response.text}")
                
        else:
            print(f"❌ 后端登录失败: {login_response.status_code}")
            print(f"错误信息: {login_response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 后端连接失败: {e}")

if __name__ == "__main__":
    test_frontend_integration()
