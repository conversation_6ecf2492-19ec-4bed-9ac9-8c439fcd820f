<template>
  <div class="amazon-config-container">
    <div class="page-header">
      <h1>Amazon API 配置管理</h1>
      <p>配置和管理亚马逊SP-API密钥、区域设置和API权限</p>
    </div>

    <!-- API配置卡片 -->
    <div class="config-cards">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>SP-API 配置</span>
            </template>
            <div class="config-item">
              <el-badge :status="spApiStatus" />
              <span class="config-label">SP-API状态</span>
            </div>
            <div class="config-actions">
              <el-button type="primary" @click="showSpApiModal = true"> 配置SP-API </el-button>
              <el-button @click="testSpApi">测试连接</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>广告API配置</span>
            </template>
            <div class="config-item">
              <el-badge :status="adsApiStatus" />
              <span class="config-label">广告API状态</span>
            </div>
            <div class="config-actions">
              <el-button type="primary" @click="showAdsApiModal = true"> 配置广告API </el-button>
              <el-button @click="testAdsApi">测试连接</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>区域设置</span>
            </template>
            <div class="config-item">
              <span class="config-label">当前区域: {{ currentRegion }}</span>
            </div>
            <div class="config-actions">
              <el-button type="primary" @click="showRegionModal = true"> 切换区域 </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- API密钥列表 -->
    <el-card class="api-keys-card">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <span>API密钥管理</span>
          <el-button type="primary" @click="addApiKey">
            <template #icon><Plus /></template>
            添加密钥
          </el-button>
        </div>
      </template>

      <el-table :data="apiKeys" row-key="id" size="small" border>
        <el-table-column prop="type" label="API类型" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="region" label="区域" />
        <el-table-column prop="environment" label="环境" />
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-badge :status="row.status === 'active' ? 'success' : 'error'" />
            <span>{{ row.status === 'active' ? '正常' : '异常' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastTest" label="最后测试" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-space>
              <el-button size="small" @click="editApiKey(row)">编辑</el-button>
              <el-button size="small" @click="testApiKey(row)">测试</el-button>
              <el-button size="small" type="danger" @click="deleteApiKey(row)">删除</el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- SP-API配置模态框 -->
    <el-dialog v-model="showSpApiModal" title="SP-API 配置" width="600px">
      <el-form :model="spApiForm" label-position="top">
        <el-form-item label="Client ID" required>
          <el-input v-model="spApiForm.clientId" placeholder="输入Client ID" />
        </el-form-item>

        <el-form-item label="Client Secret" required>
          <el-input
            v-model="spApiForm.clientSecret"
            type="password"
            placeholder="输入Client Secret"
            show-password
          />
        </el-form-item>

        <el-form-item label="Refresh Token" required>
          <el-input
            v-model="spApiForm.refreshToken"
            type="password"
            placeholder="输入Refresh Token"
            show-password
          />
        </el-form-item>

        <el-form-item label="区域" required>
          <el-select v-model="spApiForm.region" placeholder="选择区域">
            <el-option value="us-east-1" label="北美 (US)" />
            <el-option value="eu-west-1" label="欧洲 (EU)" />
            <el-option value="us-west-2" label="远东 (FE)" />
          </el-select>
        </el-form-item>

        <el-form-item label="环境">
          <el-radio-group v-model="spApiForm.environment">
            <el-radio value="sandbox">沙盒环境</el-radio>
            <el-radio value="production">生产环境</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showSpApiModal = false">取消</el-button>
        <el-button type="primary" @click="saveSpApiConfig">确定</el-button>
      </template>
    </el-dialog>

    <!-- API测试结果 -->
    <el-card v-if="testResults.length > 0" class="test-results-card">
      <template #header>
        <span>API测试结果</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="result in testResults"
          :key="result.id"
          :color="result.success ? 'green' : 'red'"
        >
          <div class="test-result-item">
            <div class="test-header">
              <span class="test-api">{{ result.api }}</span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="test-message">{{ result.message }}</div>
            <div v-if="result.data" class="test-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  // 响应式数据
  const showSpApiModal = ref(false)
  const showAdsApiModal = ref(false)
  const showRegionModal = ref(false)

  const spApiStatus = ref('success')
  const adsApiStatus = ref('error')
  const currentRegion = ref('北美 (US)')

  const spApiForm = reactive({
    clientId: '',
    clientSecret: '',
    refreshToken: '',
    region: 'us-east-1',
    environment: 'sandbox'
  })

  const testResults = ref([])

  // 注意：表格列配置已迁移到模板中的 el-table-column 组件

  const apiKeys = ref([
    {
      id: 1,
      type: 'SP-API',
      name: '北美SP-API',
      region: 'us-east-1',
      environment: 'production',
      status: 'active',
      lastTest: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      type: '广告API',
      name: '北美广告API',
      region: 'us-east-1',
      environment: 'production',
      status: 'error',
      lastTest: '2024-01-15 09:15:00'
    }
  ])

  // 方法
  const saveSpApiConfig = () => {
    // 保存SP-API配置逻辑
    ElMessage.success('SP-API配置保存成功')
    showSpApiModal.value = false
  }

  const testSpApi = async () => {
    ElMessage.loading('正在测试SP-API连接...')

    // 模拟API测试
    setTimeout(() => {
      ElMessage.closeAll()
      const result = {
        id: Date.now(),
        api: 'SP-API',
        success: true,
        message: 'SP-API连接测试成功',
        timestamp: new Date().toLocaleString(),
        data: {
          marketplaceId: 'ATVPDKIKX0DER',
          sellerId: 'A1234567890123',
          status: 'ACTIVE'
        }
      }
      testResults.value.unshift(result)
      ElMessage.success('SP-API测试成功')
    }, 2000)
  }

  const testAdsApi = async () => {
    ElMessage.loading('正在测试广告API连接...')

    setTimeout(() => {
      ElMessage.closeAll()
      const result = {
        id: Date.now(),
        api: '广告API',
        success: false,
        message: '广告API连接失败: 无效的访问令牌',
        timestamp: new Date().toLocaleString()
      }
      testResults.value.unshift(result)
      ElMessage.error('广告API测试失败')
    }, 1500)
  }

  const addApiKey = () => {
    showSpApiModal.value = true
  }

  const editApiKey = (record) => {
    ElMessage.info(`编辑API密钥: ${record.name}`)
  }

  const testApiKey = (record) => {
    if (record.type === 'SP-API') {
      testSpApi()
    } else {
      testAdsApi()
    }
  }

  const deleteApiKey = (record) => {
    ElMessage.success(`删除API密钥: ${record.name}`)
  }

  onMounted(() => {
    // 初始化数据
  })
</script>

<style lang="scss" scoped>
  .amazon-config-container {
    padding: 24px;

    .page-header {
      margin-bottom: 24px;

      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: #666;
      }
    }

    .config-cards {
      margin-bottom: 24px;

      .config-card {
        .config-item {
          margin-bottom: 16px;

          .config-label {
            margin-left: 8px;
          }
        }

        .config-actions {
          .el-button {
            margin-right: 8px;
          }
        }
      }
    }

    .api-keys-card,
    .test-results-card {
      margin-bottom: 24px;
    }

    .test-result-item {
      .test-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .test-api {
          font-weight: 500;
        }

        .test-time {
          color: #666;
          font-size: 12px;
        }
      }

      .test-message {
        margin-bottom: 8px;
      }

      .test-data {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;

        pre {
          margin: 0;
          font-size: 12px;
        }
      }
    }
  }
</style>
