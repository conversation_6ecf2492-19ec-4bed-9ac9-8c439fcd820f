from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from .models import CustomUser, Member<PERSON><PERSON><PERSON><PERSON>, AssistantProfile, Menu

class MenuSerializer(serializers.ModelSerializer):
    """菜单序列化器"""
    children = serializers.SerializerMethodField()

    class Meta:
        model = Menu
        fields = ['id', 'name', 'path', 'component', 'meta', 'parent', 'children']

    def get_children(self, obj):
        # 递归地序列化子菜单
        children = obj.children.using('default').all()
        if children:
            return MenuSerializer(children, many=True, context=self.context).data
        return None

from .middleware import get_current_region


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    userName = serializers.CharField(max_length=150, help_text="用户名")
    password = serializers.Char<PERSON><PERSON>(max_length=128, write_only=True, help_text="密码")

    def validate(self, attrs):
        """验证用户名和密码"""
        username = attrs.get('userName')
        password = attrs.get('password')

        if not username or not password:
            raise serializers.ValidationError("用户名和密码不能为空")

        # 使用Django的authenticate方法验证用户
        user = authenticate(username=username, password=password)

        if not user:
            raise serializers.ValidationError("用户名或密码错误")

        if not user.is_active:
            raise serializers.ValidationError("用户账号已被禁用，请联系管理员")

        attrs['user'] = user
        return attrs

    def create_tokens(self, user):
        """为用户创建JWT tokens"""
        refresh = RefreshToken.for_user(user)
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }


class LoginResponseSerializer(serializers.Serializer):
    """登录响应序列化器"""
    token = serializers.CharField(help_text="访问令牌")
    refreshToken = serializers.CharField(help_text="刷新令牌")
    userInfo = serializers.DictField(help_text="用户信息")


class UserInfoSerializer(serializers.ModelSerializer):
    """用户信息序列化器（用于登录响应）"""
    roleDisplay = serializers.CharField(source='get_role_display', read_only=True)
    regionName = serializers.CharField(source='region.name', read_only=True)
    regionCode = serializers.CharField(source='region.code', read_only=True)
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'phone', 'role', 'roleDisplay',
            'regionName', 'regionCode', 'permissions', 'mws_switch'
        ]

    def get_permissions(self, obj):
        """获取用户权限代码列表"""
        return obj.get_permission_codes()

class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器 - 支持区域感知注册"""
    
    password = serializers.CharField(
        write_only=True, 
        min_length=8, 
        style={'input_type': 'password'}
    )
    password2 = serializers.CharField(
        write_only=True, 
        style={'input_type': 'password'}
    )
    
    class Meta:
        model = CustomUser
        fields = ('username', 'password', 'password2', 'phone')
        extra_kwargs = {
            'phone': {'required': False}
        }

    def validate(self, attrs):
        """验证密码一致性和区域要求"""
        
        # 1. 验证两次密码是否一致
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({
                'password2': '两次输入的密码不一致'
            })
        
        # 2. 验证密码强度
        try:
            validate_password(attrs['password'])
        except ValidationError as e:
            raise serializers.ValidationError({
                'password': list(e.messages)
            })
        
        # 3. 获取当前区域，确保区域感知
        region = get_current_region()
        if not region:
            raise serializers.ValidationError({
                'region': '注册请求必须包含有效的区域代码(X-Region-Code请求头)'
            })
        
        # 将区域信息保存到验证后的数据中，供create方法使用
        attrs['region'] = region
        
        return attrs

    def validate_username(self, value):
        """验证用户名在当前区域内是否唯一"""
        region = get_current_region()
        if region:
            # 使用区域感知管理器检查用户名是否已存在
            if CustomUser.objects.filter(username=value).exists():
                raise serializers.ValidationError(
                    f'用户名 "{value}" 在当前区域已存在'
                )
        return value



    def create(self, validated_data):
        """创建新用户，自动关联区域和默认角色"""
        
        # 移除确认密码字段
        validated_data.pop('password2', None)
        
        # 获取区域信息
        region = validated_data.pop('region')
        
        # 提取密码
        password = validated_data.pop('password')
        
        # 创建用户实例
        user = CustomUser(
            **validated_data,
            region=region,
            role=CustomUser.Role.MEMBER  # 默认为普通会员
        )
        
        # 设置加密密码
        user.set_password(password)
        user.save()
        
        return user

    def to_representation(self, instance):
        """自定义返回数据，不包含敏感信息"""
        data = super().to_representation(instance)
        # 移除密码字段
        data.pop('password', None)
        data.pop('password2', None)
        
        # 添加一些有用的信息
        data.update({
            'id': instance.id,
            'region': instance.region.name if instance.region else None,
            'region_code': instance.region.code if instance.region else None,
            'role': instance.get_role_display(),
            'date_joined': instance.date_joined.isoformat() if instance.date_joined else None
        })
        
        return data


class MemberProfileSerializer(serializers.ModelSerializer):
    """会员资料序列化器"""

    class Meta:
        model = MemberProfile
        fields = [
            'real_name', 'english_name', 'description', 'image_server_url', 'document_server_url',
            'finance_auth_token', 'sku_prefix', 'payment_channel', 'parent_account', 
            'ems_vip_number', 'ems_customer_code', 'ems_version_info', 'international_eub_us', 'ems_auth_token',
            'amazon_username', 'amazon_seller_id', 'amazon_marketplace_id',
            'aws_access_key_id', 'aws_secret_key', 'merchant_token',
            'developer_aws_access', 'developer_secret_key', 'mws_auth_token',
            'warehouse_location', 'status', 'exempt_brands'
        ]
        extra_kwargs = {
            # 敏感字段设置为write_only
            'finance_auth_token': {'write_only': True},
            'ems_auth_token': {'write_only': True},
            'aws_access_key_id': {'write_only': True},
            'aws_secret_key': {'write_only': True},
            'merchant_token': {'write_only': True},
            'developer_aws_access': {'write_only': True},
            'developer_secret_key': {'write_only': True},
            'mws_auth_token': {'write_only': True},
        }


class AssistantProfileSerializer(serializers.ModelSerializer):
    """助理资料序列化器"""

    class Meta:
        model = AssistantProfile
        fields = [
            'description', 'related_finance_auth_token', 'internal_member_finance_auth_token'
        ]
        extra_kwargs = {
            # 敏感字段设置为write_only
            'related_finance_auth_token': {'write_only': True},
            'internal_member_finance_auth_token': {'write_only': True},
        }


class UserDetailSerializer(serializers.ModelSerializer):
    """用户详情序列化器 - 支持Profile数据"""

    member_profile = MemberProfileSerializer(read_only=True)
    assistant_profile = AssistantProfileSerializer(read_only=True)

    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'phone', 'role', 'region',
            'date_joined', 'last_login', 'is_active',
            'member_profile', 'assistant_profile'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def to_representation(self, instance):
        """根据角色动态返回Profile数据"""
        data = super().to_representation(instance)

        # 添加角色显示名称（使用驼峰命名）
        data['roleDisplay'] = instance.get_role_display()
        data['regionName'] = instance.region.name if instance.region else None
        data['regionCode'] = instance.region.code if instance.region else None

        # 根据角色决定是否包含Profile数据
        if instance.role == CustomUser.Role.MEMBER:
            # 会员角色：包含member_profile，移除assistant_profile
            data.pop('assistant_profile', None)
        elif instance.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ]:
            # 助理角色：包含assistant_profile，移除member_profile
            data.pop('member_profile', None)
        else:
            # 其他角色：移除所有Profile数据
            data.pop('member_profile', None)
            data.pop('assistant_profile', None)

        return data


class UserCreateUpdateSerializer(serializers.ModelSerializer):
    """用户创建/更新序列化器 - 支持Profile数据"""

    member_profile = MemberProfileSerializer(required=False)
    assistant_profile = AssistantProfileSerializer(required=False)
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = CustomUser
        fields = [
            'username', 'phone', 'role', 'password', 'is_active', 'mws_switch', 'refresh_token',
            'member_profile', 'assistant_profile'
        ]
        extra_kwargs = {
            'refresh_token': {'write_only': True},  # 刷新令牌只能写入，不在序列化时返回
        }

    def validate(self, attrs):
        """验证数据"""
        role = attrs.get('role')
        member_profile = attrs.get('member_profile')
        assistant_profile = attrs.get('assistant_profile')

        # 根据角色验证Profile数据
        if role == CustomUser.Role.MEMBER and not member_profile:
            # 会员角色可以没有Profile数据（可选）
            pass
        elif role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and not assistant_profile:
            # 助理角色可以没有Profile数据（可选）
            pass

        return attrs

    def create(self, validated_data):
        """创建用户和Profile"""
        member_profile_data = validated_data.pop('member_profile', None)
        assistant_profile_data = validated_data.pop('assistant_profile', None)
        password = validated_data.pop('password', None)

        # 注意：区域信息应该在调用save()时通过参数传递，而不是从全局函数获取
        # 如果没有传递区域信息，尝试从全局函数获取（向后兼容）
        if 'region' not in validated_data:
            region = get_current_region()
            if region:
                validated_data['region'] = region

        # 创建用户
        user = CustomUser.objects.create(**validated_data)
        if password:
            user.set_password(password)
        else:
            # 如果没有提供密码，设置默认密码
            user.set_password('temp123456')
        user.save()

        # 根据角色创建对应的Profile
        if user.role == CustomUser.Role.MEMBER and member_profile_data:
            MemberProfile.objects.create(user=user, **member_profile_data)
        elif user.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and assistant_profile_data:
            AssistantProfile.objects.create(user=user, **assistant_profile_data)

        return user

    def update(self, instance, validated_data):
        """更新用户和Profile"""
        member_profile_data = validated_data.pop('member_profile', None)
        assistant_profile_data = validated_data.pop('assistant_profile', None)
        password = validated_data.pop('password', None)

        # 更新用户基础信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()

        # 更新Profile数据
        if instance.role == CustomUser.Role.MEMBER and member_profile_data:
            profile, created = MemberProfile.objects.get_or_create(user=instance)
            for attr, value in member_profile_data.items():
                setattr(profile, attr, value)
            profile.save()
        elif instance.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and assistant_profile_data:
            profile, created = AssistantProfile.objects.get_or_create(user=instance)
            for attr, value in assistant_profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance