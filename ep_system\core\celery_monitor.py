"""
Celery监控工具
"""
from celery import current_app
from django.conf import settings
import redis
import logging

logger = logging.getLogger(__name__)

def get_celery_worker_status():
    """
    获取Celery Worker状态
    """
    try:
        # 检查Celery应用状态
        inspect = current_app.control.inspect()
        
        # 获取活跃的workers
        active_workers = inspect.active()
        
        # 获取注册的任务
        registered_tasks = inspect.registered()
        
        # 获取统计信息
        stats = inspect.stats()
        
        if active_workers:
            worker_count = len(active_workers)
            status = 'running'
            response_time = 5  # 模拟响应时间
        else:
            worker_count = 0
            status = 'error'
            response_time = 0
            
        return {
            'status': status,
            'worker_count': worker_count,
            'response_time': response_time,
            'active_workers': active_workers,
            'registered_tasks': registered_tasks,
            'stats': stats
        }
    except Exception as e:
        logger.error(f"获取Celery状态失败: {str(e)}")
        return {
            'status': 'error',
            'worker_count': 0,
            'response_time': 0,
            'error': str(e)
        }

def get_redis_status():
    """
    获取Redis状态
    """
    try:
        # 连接Redis
        r = redis.Redis.from_url(settings.CELERY_BROKER_URL)
        
        # 测试连接
        r.ping()
        
        # 获取信息
        info = r.info()
        
        return {
            'status': 'connected',
            'version': info.get('redis_version', 'unknown'),
            'memory_usage': info.get('used_memory_human', 'unknown'),
            'connected_clients': info.get('connected_clients', 0),
            'response_time': 2
        }
    except Exception as e:
        logger.error(f"获取Redis状态失败: {str(e)}")
        return {
            'status': 'error',
            'error': str(e),
            'response_time': 0
        }

def get_task_queue_info():
    """
    获取任务队列信息
    """
    try:
        inspect = current_app.control.inspect()
        
        # 获取队列中的任务
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        reserved_tasks = inspect.reserved()
        
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())
        total_reserved = sum(len(tasks) for tasks in (reserved_tasks or {}).values())
        
        return {
            'active_tasks': total_active,
            'scheduled_tasks': total_scheduled,
            'reserved_tasks': total_reserved,
            'total_tasks': total_active + total_scheduled + total_reserved
        }
    except Exception as e:
        logger.error(f"获取任务队列信息失败: {str(e)}")
        return {
            'active_tasks': 0,
            'scheduled_tasks': 0,
            'reserved_tasks': 0,
            'total_tasks': 0,
            'error': str(e)
        }