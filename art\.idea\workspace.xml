<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="36319710-8f62-44c4-9ef7-7fdc4fa0fc9d" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.env" beforeDir="false" afterPath="$PROJECT_DIR$/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.zh-CN.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.zh-CN.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/clean-dev.ts" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/clean-dev.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/menuApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/menuApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/usersApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/usersApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/config/dataSource.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/config/dataSource.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/config/headerBar.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/config/headerBar.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/config/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/config/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/locales/langs/en.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/locales/langs/en.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/locales/langs/zh.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/locales/langs/zh.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/guards/beforeEach.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/guards/beforeEach.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/routesAlias.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/routesAlias.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/utils/registerRoutes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/utils/registerRoutes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/worktab.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/worktab.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/typings/api.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/typings/api.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/http/error.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/http/error.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/http/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/http/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/sys/console.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/auth/login/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/auth/login/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/examples/tables/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/examples/tables/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/index/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/index/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/menu/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/menu/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/user-center/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/user-center/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/user/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/user/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/user/modules/user-dialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/user/modules/user-dialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/user/modules/user-search.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/user/modules/user-search.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/text-scroll/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/text-scroll/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="SWAP_SIDES_IN_COMPARE_BRANCHES" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zr7tGfmtFTEg4ioyDyJKhMS8Pu" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;my&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;D:\\web\\Dawn_ERP_V1.0\\art\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\web\\Dawn_ERP_V1.0\\art\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.CommitDialog&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\web\\Dawn_ERP_V1.0\\art\\node_modules\\typescript\\lib&quot;,
    &quot;two.files.diff.last.used.folder&quot;: &quot;D:/web/Dawn_ERP_V1.0/art - 副本/src&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-WS-251.25410.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="36319710-8f62-44c4-9ef7-7fdc4fa0fc9d" name="更改" comment="" />
      <created>1752477551999</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752477551999</updated>
      <workItem from="1752477553883" duration="8788000" />
      <workItem from="1752544980745" duration="10731000" />
      <workItem from="1752736213485" duration="38000" />
      <workItem from="1752736436069" duration="302000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
</project>