#!/usr/bin/env python3
"""
创建开发者菜单系统
包括亚马逊API管理和其他开发者工具
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('ep_system')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
django.setup()

from core.models import Menu, Permission, CustomUser

def create_developer_role():
    """创建开发者角色相关权限"""
    print("🔧 创建开发者权限...")
    
    # 开发者专用权限
    developer_permissions = [
        # 系统开发权限
        {'name': '系统开发', 'code': 'system_development', 'description': '系统开发和调试权限', 'category': '开发者权限'},
        {'name': 'API管理', 'code': 'api_management', 'description': '管理各种API接口和配置', 'category': '开发者权限'},
        {'name': '数据库管理', 'code': 'database_management', 'description': '数据库查询和管理工具', 'category': '开发者权限'},
        {'name': '系统监控', 'code': 'system_monitoring', 'description': '系统性能监控和日志查看', 'category': '开发者权限'},
        {'name': '测试工具', 'code': 'testing_tools', 'description': '测试数据生成和功能测试', 'category': '开发者权限'},
        
        # 亚马逊API相关权限
        {'name': '亚马逊API配置', 'code': 'amazon_api_config', 'description': '配置亚马逊API密钥和设置', 'category': '亚马逊API'},
        {'name': '亚马逊数据同步', 'code': 'amazon_data_sync', 'description': '同步亚马逊商品和订单数据', 'category': '亚马逊API'},
        {'name': '亚马逊报告管理', 'code': 'amazon_report_management', 'description': '管理亚马逊各类报告', 'category': '亚马逊API'},
        {'name': '亚马逊广告管理', 'code': 'amazon_ads_management', 'description': '管理亚马逊广告API', 'category': '亚马逊API'},
    ]
    
    created_count = 0
    for perm_data in developer_permissions:
        permission, created = Permission.objects.get_or_create(
            code=perm_data['code'],
            defaults={
                'name': perm_data['name'],
                'description': perm_data['description'],
                'category': perm_data['category']
            }
        )
        
        if created:
            created_count += 1
            print(f"  ✅ {perm_data['name']} - {perm_data['description']}")
        else:
            print(f"  ⚠️ 已存在: {perm_data['name']}")
    
    print(f"📊 开发者权限创建完成，新增 {created_count} 个权限")

def create_developer_menus():
    """创建开发者菜单"""
    print("\n🛠️ 创建开发者菜单...")
    
    # 创建开发者控制台主菜单
    developer_menu = Menu.objects.create(
        name='开发者控制台',
        path='/developer',
        component='/index/index',
        meta={'title': 'menus.developer.title', 'icon': '&#xe7d0;'}
    )
    
    # 开发者子菜单
    developer_submenus = [
        # 系统工具
        {'name': '系统监控', 'path': 'dev-system-monitor', 'component': '/developer/system-monitor', 'title': 'menus.developer.systemMonitor'},
        {'name': '数据库工具', 'path': 'dev-database-tools', 'component': '/developer/database-tools', 'title': 'menus.developer.databaseTools'},
        {'name': 'API调试', 'path': 'dev-api-debug', 'component': '/developer/api-debug', 'title': 'menus.developer.apiDebug'},
        {'name': '日志查看', 'path': 'dev-logs', 'component': '/developer/logs', 'title': 'menus.developer.logs'},
        {'name': '性能分析', 'path': 'dev-performance', 'component': '/developer/performance', 'title': 'menus.developer.performance'},
        
        # 测试工具
        {'name': '测试数据生成', 'path': 'dev-test-data', 'component': '/developer/test-data', 'title': 'menus.developer.testData'},
        {'name': '用户模拟', 'path': 'dev-user-simulation', 'component': '/developer/user-simulation', 'title': 'menus.developer.userSimulation'},
        {'name': '功能测试', 'path': 'dev-function-test', 'component': '/developer/function-test', 'title': 'menus.developer.functionTest'},
        
        # 亚马逊API管理
        {'name': 'Amazon API配置', 'path': 'dev-amazon-config', 'component': '/developer/amazon-config', 'title': 'menus.developer.amazonConfig'},
        {'name': 'Amazon数据同步', 'path': 'dev-amazon-sync', 'component': '/developer/amazon-sync', 'title': 'menus.developer.amazonSync'},
        {'name': 'Amazon报告管理', 'path': 'dev-amazon-reports', 'component': '/developer/amazon-reports', 'title': 'menus.developer.amazonReports'},
        {'name': 'Amazon广告API', 'path': 'dev-amazon-ads', 'component': '/developer/amazon-ads', 'title': 'menus.developer.amazonAds'},
        {'name': 'Amazon SP-API测试', 'path': 'dev-amazon-sp-api', 'component': '/developer/amazon-sp-api', 'title': 'menus.developer.amazonSpApi'},
        
        # 系统配置
        {'name': '环境变量', 'path': 'dev-env-config', 'component': '/developer/env-config', 'title': 'menus.developer.envConfig'},
        {'name': '功能开关', 'path': 'dev-feature-flags', 'component': '/developer/feature-flags', 'title': 'menus.developer.featureFlags'},
        {'name': '缓存管理', 'path': 'dev-cache-management', 'component': '/developer/cache-management', 'title': 'menus.developer.cacheManagement'},
        {'name': '任务调度', 'path': 'dev-task-scheduler', 'component': '/developer/task-scheduler', 'title': 'menus.developer.taskScheduler'},
    ]
    
    for submenu_data in developer_submenus:
        Menu.objects.create(
            name=submenu_data['name'],
            path=submenu_data['path'],
            component=submenu_data['component'],
            meta={'title': submenu_data['title'], 'keepAlive': True},
            parent=developer_menu
        )
        print(f"  ✅ {submenu_data['name']}")
    
    print(f"📊 开发者菜单创建完成，共 {len(developer_submenus)} 个子菜单")

def create_developer_user():
    """创建开发者用户"""
    print("\n👨‍💻 创建开发者用户...")
    
    try:
        # 检查是否已存在开发者用户
        developer_user, created = CustomUser.objects.get_or_create(
            username='developer',
            defaults={
                'password': 'pbkdf2_sha256$600000$dummy$dummy',  # 临时密码
                'role': CustomUser.Role.SUPER_ADMIN,  # 暂时使用超级管理员角色
                'is_active': True,
                'is_staff': True,
            }
        )
        
        if created:
            # 设置密码
            developer_user.set_password('dev123456')
            developer_user.save()
            print(f"✅ 开发者用户创建成功")
            print(f"   用户名: developer")
            print(f"   密码: dev123456")
        else:
            print(f"⚠️ 开发者用户已存在: {developer_user.username}")
            
    except Exception as e:
        print(f"❌ 创建开发者用户失败: {e}")

def show_developer_menu_summary():
    """显示开发者菜单总结"""
    print(f"\n📋 开发者控制台功能总结:")
    
    categories = {
        "🔧 系统工具": [
            "系统监控 - 服务状态、性能指标、错误统计",
            "数据库工具 - SQL查询、数据导入导出、结构查看",
            "API调试 - 接口测试、请求日志、响应分析",
            "日志查看 - 应用日志、错误日志、用户操作日志",
            "性能分析 - 慢查询分析、接口响应时间、资源使用"
        ],
        "🧪 测试工具": [
            "测试数据生成 - 批量生成测试用户、产品、订单",
            "用户模拟 - 模拟不同角色用户进行功能测试",
            "功能测试 - 自动化测试、接口测试、压力测试"
        ],
        "🛒 亚马逊API管理": [
            "Amazon API配置 - SP-API密钥配置、区域设置",
            "Amazon数据同步 - 商品信息、库存、订单同步",
            "Amazon报告管理 - 销售报告、库存报告、广告报告",
            "Amazon广告API - 广告活动管理、关键词优化",
            "Amazon SP-API测试 - API接口测试和调试"
        ],
        "⚙️ 系统配置": [
            "环境变量 - 系统配置参数管理",
            "功能开关 - 新功能灰度发布控制",
            "缓存管理 - Redis缓存查看和清理",
            "任务调度 - 定时任务管理和监控"
        ]
    }
    
    for category, features in categories.items():
        print(f"\n{category}:")
        for feature in features:
            print(f"  • {feature}")
    
    print(f"\n🎯 开发者账户信息:")
    print(f"  用户名: developer")
    print(f"  密码: dev123456")
    print(f"  角色: 开发者")
    print(f"  权限: 完整的开发和调试权限")

def main():
    print("🚀 开始创建开发者菜单系统...")
    
    # 创建开发者权限
    create_developer_role()
    
    # 创建开发者菜单
    create_developer_menus()
    
    # 创建开发者用户
    create_developer_user()
    
    # 显示总结
    show_developer_menu_summary()
    
    print("\n🎉 开发者菜单系统创建完成！")
    print("\n📝 下一步:")
    print("  1. 使用 developer/dev123456 登录系统")
    print("  2. 查看开发者控制台菜单")
    print("  3. 开始配置亚马逊API")
    print("  4. 使用各种开发工具进行系统开发")

if __name__ == "__main__":
    main()
