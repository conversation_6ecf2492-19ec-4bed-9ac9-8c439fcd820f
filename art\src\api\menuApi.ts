import { useRealAPI } from '@/config/dataSource'
import { asyncRoutes } from '@/router/routes/asyncRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import request from '@/utils/http'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 菜单接口
export const menuService = {
  async getMenuList(delay = 300): Promise<MenuResponse> {
    try {
      // 检查是否使用真实API
      if (useRealAPI('menuSystem')) {
        console.log('🚀 使用真实菜单API')
        // 从后端获取动态菜单
        const response = await request.get<{ menuList: any[] }>({
          url: '/api/v1/menu/list/'
        })

        console.log('✅ 菜单API调用成功:', response)

        // 处理后端返回的菜单数据，转换格式
        const processedMenuList = response.menuList.map((menu) => {
          // 解析 meta 字段（从JSON字符串转换为对象）
          let meta = {}
          try {
            meta = typeof menu.meta === 'string' ? JSON.parse(menu.meta) : menu.meta
          } catch (e) {
            console.warn('解析菜单meta失败:', menu.meta)
            meta = {}
          }

          // 处理组件路径
          let component = menu.component
          if (component === 'Layout') {
            component = '/index/index'
          } else if (component && !component.startsWith('/')) {
            component = `/${component}`
          }

          const processedMenu: AppRouteRecord = {
            name: menu.name,
            path: menu.path,
            component: component,
            meta: meta,
            children: menu.children
              ? menu.children.map((child: any) => {
                  // 递归处理子菜单
                  let childMeta = {}
                  try {
                    childMeta = typeof child.meta === 'string' ? JSON.parse(child.meta) : child.meta
                  } catch (e) {
                    console.warn('解析子菜单meta失败:', child.meta)
                    childMeta = {}
                  }

                  let childComponent = child.component
                  if (childComponent && !childComponent.startsWith('/')) {
                    childComponent = `/${childComponent}`
                  }

                  return {
                    name: child.name,
                    path: child.path,
                    component: childComponent,
                    meta: childMeta
                  }
                })
              : []
          }

          return processedMenu
        })

        // 使用 menuDataToRouter 进一步处理
        const menuList = processedMenuList.map((route) => menuDataToRouter(route))
        console.log('📋 处理后的菜单数据:', menuList)
        return { menuList }
      } else {
        console.log('📝 使用静态菜单配置')
        // 使用静态菜单配置（保持现有功能）
        const menuData = asyncRoutes
        // 处理菜单数据
        const menuList = menuData.map((route) => menuDataToRouter(route))
        // 模拟接口延迟
        await new Promise((resolve) => setTimeout(resolve, delay))
        return { menuList }
      }
    } catch (error) {
      // 如果真实API失败，回退到静态菜单
      console.error('❌ 动态菜单获取失败，回退到静态菜单:', error)
      const menuData = asyncRoutes
      const menuList = menuData.map((route) => menuDataToRouter(route))
      return { menuList }
    }
  },

  async createMenu(menuData: any): Promise<any> {
    return await request.post({ url: '/api/v1/menu/create/', data: menuData })
  }
}
